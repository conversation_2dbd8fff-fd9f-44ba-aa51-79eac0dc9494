globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/pages/team-management/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamMemberManagement.tsx"));
            var _MemberAccountManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/MemberAccountManagement.tsx"));
            var _TeamSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamSettings.tsx"));
            var _team = __mako_require__("src/services/team.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const TeamManagementPage = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
                const [activeTab, setActiveTab] = (0, _react.useState)('members');
                (0, _react.useEffect)(()=>{
                    fetchTeamDetail();
                }, []);
                const fetchTeamDetail = async ()=>{
                    try {
                        setLoading(true);
                        const detail = await _team.TeamService.getCurrentTeamDetail();
                        setTeamDetail(detail);
                    } catch (error) {
                        console.error('获取团队详情失败:', error);
                        // 如果获取失败，可能是没有选择团队，跳转到团队选择页面
                        _max.history.push('/user/team-select');
                    } finally{
                        setLoading(false);
                    }
                };
                // 权限检查：只有团队创建者可以访问管理功能
                const hasManagePermission = (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false;
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 79,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "正在加载团队信息..."
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 80,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 77,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                textAlign: 'center',
                                padding: '50px 0'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InfoCircleOutlined, {
                                    style: {
                                        fontSize: 48,
                                        color: '#faad14',
                                        marginBottom: 16
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 93,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    children: "未找到团队信息"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 94,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "请先选择一个团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginTop: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        onClick: ()=>_max.history.push('/user/team-select'),
                                        children: "选择团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 97,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 96,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 90,
                    columnNumber: 7
                }, this);
                // 权限不足提示
                if (!hasManagePermission) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "权限不足",
                            description: "只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。",
                            type: "warning",
                            showIcon: true,
                            action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                size: "small",
                                onClick: ()=>_max.history.push('/dashboard'),
                                children: "返回首页"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 118,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 110,
                    columnNumber: 7
                }, this);
                // 选项卡配置
                const tabItems = [
                    {
                        key: 'members',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 134,
                                    columnNumber: 11
                                }, this),
                                "团队成员管理"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 133,
                            columnNumber: 9
                        }, this),
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberManagement.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 139,
                            columnNumber: 9
                        }, this)
                    },
                    {
                        key: 'accounts',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 149,
                                    columnNumber: 11
                                }, this),
                                "成员账户管理"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 148,
                            columnNumber: 9
                        }, this),
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_MemberAccountManagement.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 154,
                            columnNumber: 9
                        }, this)
                    },
                    {
                        key: 'settings',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 164,
                                    columnNumber: 11
                                }, this),
                                "团队设置"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 163,
                            columnNumber: 9
                        }, this),
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamSettings.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 169,
                            columnNumber: 9
                        }, this)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    title: "团队管理",
                    subTitle: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 182,
                                    columnNumber: 38
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 182,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: teamDetail.name
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 183,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 184,
                                    columnNumber: 22
                                }, void 0),
                                color: "gold",
                                children: "管理员"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 184,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 181,
                        columnNumber: 9
                    }, void 0),
                    extra: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            onClick: ()=>_max.history.push('/dashboard'),
                            children: "返回首页"
                        }, "back", false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 188,
                            columnNumber: 9
                        }, void 0)
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: setActiveTab,
                            items: tabItems,
                            size: "large",
                            tabBarStyle: {
                                marginBottom: 24
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 197,
                            columnNumber: 9
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 196,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 178,
                    columnNumber: 5
                }, this);
            };
            _s(TeamManagementPage, "MbpIoVQJb3zY91BHiEpQzi7JPmo=");
            _c = TeamManagementPage;
            var _default = TeamManagementPage;
            var _c;
            $RefreshReg$(_c, "TeamManagementPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team-management/components/TeamMemberManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _team = __mako_require__("src/services/team.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
                const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
                const [inviteForm] = _antd.Form.useForm();
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        _antd.message.error('获取团队成员失败');
                        setMembers([]); // 确保在错误时设置为空数组
                    } finally{
                        setLoading(false);
                    }
                };
                // 邀请新成员
                const handleInviteMembers = async (values)=>{
                    try {
                        const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
                        await _team.TeamService.inviteMembers({
                            emails: emailList
                        });
                        _antd.message.success(`成功邀请 ${emailList.length} 名成员`);
                        setInviteModalVisible(false);
                        inviteForm.resetFields();
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('邀请成员失败:', error);
                        _antd.message.error('邀请成员失败');
                    }
                };
                // 移除单个成员
                const handleRemoveMember = async (member)=>{
                    try {
                        await _team.TeamService.removeMember(member.id);
                        _antd.message.success(`已移除成员：${member.name}`);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('移除成员失败:', error);
                        _antd.message.error('移除成员失败');
                    }
                };
                // 批量移除成员
                const handleBatchRemove = async ()=>{
                    try {
                        const memberIds = selectedRowKeys;
                        for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
                        _antd.message.success(`已移除 ${memberIds.length} 名成员`);
                        setSelectedRowKeys([]);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('批量移除成员失败:', error);
                        _antd.message.error('批量移除成员失败');
                    }
                };
                // 筛选成员
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                // 表格列配置
                const columns = [
                    {
                        title: '成员信息',
                        key: 'member',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: "large",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 152,
                                            columnNumber: 38
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 152,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: record.name
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 156,
                                                            columnNumber: 17
                                                        }, this),
                                                        record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 158,
                                                                columnNumber: 30
                                                            }, void 0),
                                                            color: "gold",
                                                            children: "创建者"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 158,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 155,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 154,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: 12
                                                },
                                                children: record.email
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 162,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 153,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 151,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'status',
                        width: 100,
                        render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: isActive ? 'green' : 'orange',
                                children: isActive ? '活跃' : '待激活'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 175,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '加入时间',
                        dataIndex: 'assignedAt',
                        key: 'assignedAt',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 120,
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 200,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                title: "确认移除成员",
                                description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                                onConfirm: ()=>handleRemoveMember(record),
                                okText: "确认",
                                cancelText: "取消",
                                okType: "danger",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    danger: true,
                                    size: "small",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 216,
                                        columnNumber: 21
                                    }, void 0),
                                    children: "移除"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 212,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 204,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                // 行选择配置
                const rowSelection = {
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record)=>({
                            disabled: record.isCreator
                        })
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: 16,
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员总数",
                                            value: (members || []).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 244,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 241,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 240,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 239,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "活跃成员",
                                            value: (members || []).filter((m)=>m.isActive).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 253,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 250,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 249,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 248,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "待激活成员",
                                            value: (members || []).filter((m)=>!m.isActive).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 262,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 259,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 258,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 257,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "管理员",
                                            value: (members || []).filter((m)=>m.isCreator).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 271,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 268,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 267,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 266,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 238,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                justify: "space-between",
                                align: "middle",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "搜索成员姓名或邮箱",
                                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 284,
                                                        columnNumber: 25
                                                    }, void 0),
                                                    value: searchText,
                                                    onChange: (e)=>setSearchText(e.target.value),
                                                    style: {
                                                        width: 300
                                                    },
                                                    allowClear: true
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 282,
                                                    columnNumber: 15
                                                }, this),
                                                selectedRowKeys.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                    title: "批量移除成员",
                                                    description: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`,
                                                    onConfirm: handleBatchRemove,
                                                    okText: "确认",
                                                    cancelText: "取消",
                                                    okType: "danger",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        danger: true,
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 301,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        children: [
                                                            "批量移除 (",
                                                            selectedRowKeys.length,
                                                            ")"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 299,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 291,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 281,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 280,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 312,
                                                columnNumber: 21
                                            }, void 0),
                                            onClick: ()=>setInviteModalVisible(true),
                                            children: "邀请成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 310,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 309,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 279,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 278,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                columns: columns,
                                dataSource: filteredMembers,
                                rowKey: "id",
                                loading: loading,
                                rowSelection: rowSelection,
                                pagination: {
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total)=>`共 ${total} 名成员`,
                                    pageSize: 10
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 323,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 322,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "邀请新成员",
                            open: inviteModalVisible,
                            onCancel: ()=>{
                                setInviteModalVisible(false);
                                inviteForm.resetFields();
                            },
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: inviteForm,
                                layout: "vertical",
                                onFinish: handleInviteMembers,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "emails",
                                        label: "邮箱地址",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱地址'
                                            }
                                        ],
                                        extra: "每行一个邮箱地址，支持批量邀请",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 6,
                                            placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 362,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 354,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 369,
                                                        columnNumber: 62
                                                    }, void 0),
                                                    children: "发送邀请"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 369,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setInviteModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 372,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 368,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 367,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 349,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 339,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 236,
                    columnNumber: 5
                }, this);
            };
            _s(TeamMemberManagement, "lwyw8TlHxmNVMk/bokFh1nGrhdE=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TeamMemberManagement;
            var _default = TeamMemberManagement;
            var _c;
            $RefreshReg$(_c, "TeamMemberManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team-management/components/MemberAccountManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _team = __mako_require__("src/services/team.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            const MemberAccountManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                const [selectedMember, setSelectedMember] = (0, _react.useState)(null);
                const [settingsForm] = _antd.Form.useForm();
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        _antd.message.error('获取团队成员失败');
                        setMembers([]); // 确保在错误时设置为空数组
                    } finally{
                        setLoading(false);
                    }
                };
                // 切换成员活跃状态
                const handleToggleActiveStatus = async (member, isActive)=>{
                    try {
                        // 这里应该调用API来更新成员状态
                        // 由于当前后端可能没有这个接口，我们先模拟
                        _antd.message.success(`已${isActive ? '激活' : '停用'}成员：${member.name}`);
                        fetchMembers();
                    } catch (error) {
                        console.error('更新成员状态失败:', error);
                        _antd.message.error('更新成员状态失败');
                    }
                };
                // 打开成员设置弹窗
                const handleOpenSettings = (member)=>{
                    setSelectedMember(member);
                    settingsForm.setFieldsValue({
                        role: member.isCreator ? 'admin' : 'member',
                        isActive: member.isActive
                    });
                    setSettingsModalVisible(true);
                };
                // 保存成员设置
                const handleSaveSettings = async (values)=>{
                    try {
                        // 这里应该调用API来更新成员设置
                        // 由于当前后端可能没有这个接口，我们先模拟
                        _antd.message.success(`已更新成员设置：${selectedMember === null || selectedMember === void 0 ? void 0 : selectedMember.name}`);
                        setSettingsModalVisible(false);
                        fetchMembers();
                    } catch (error) {
                        console.error('更新成员设置失败:', error);
                        _antd.message.error('更新成员设置失败');
                    }
                };
                // 筛选成员
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                // 表格列配置
                const columns = [
                    {
                        title: '成员信息',
                        key: 'member',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: "large",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 138,
                                            columnNumber: 38
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 138,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: record.name
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                            lineNumber: 142,
                                                            columnNumber: 17
                                                        }, this),
                                                        record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                                lineNumber: 144,
                                                                columnNumber: 30
                                                            }, void 0),
                                                            color: "gold",
                                                            children: "管理员"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                            lineNumber: 144,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                    lineNumber: 141,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                lineNumber: 140,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: 12
                                                },
                                                children: record.email
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                lineNumber: 148,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 139,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 137,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '角色',
                        key: 'role',
                        width: 120,
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: record.isCreator ? 'gold' : 'blue',
                                children: record.isCreator ? '管理员' : '成员'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 160,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '账户状态',
                        key: 'accountStatus',
                        width: 120,
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: record.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 172,
                                        columnNumber: 24
                                    }, void 0),
                                    color: "success",
                                    children: "活跃"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 172,
                                    columnNumber: 13
                                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseCircleOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 176,
                                        columnNumber: 24
                                    }, void 0),
                                    color: "warning",
                                    children: "待激活"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 176,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 170,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '权限控制',
                        key: 'permissions',
                        width: 120,
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "管理员"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 196,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: record.isActive ? '点击停用' : '点击激活',
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                                    checked: record.isActive,
                                    onChange: (checked)=>handleToggleActiveStatus(record, checked),
                                    size: "small"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 201,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 200,
                                columnNumber: 11
                            }, this);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 100,
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 216,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 223,
                                    columnNumber: 19
                                }, void 0),
                                onClick: ()=>handleOpenSettings(record),
                                children: "设置"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 220,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: 16,
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "总成员数",
                                            value: (members || []).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                lineNumber: 242,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 239,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 238,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 237,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "活跃账户",
                                            value: (members || []).filter((m)=>m.isActive).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                lineNumber: 251,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#3f8600'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 248,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 247,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 246,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "待激活账户",
                                            value: (members || []).filter((m)=>!m.isActive).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseCircleOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                lineNumber: 261,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#cf1322'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 258,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 257,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 256,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "管理员",
                                            value: (members || []).filter((m)=>m.isCreator).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                lineNumber: 271,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#faad14'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 268,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 267,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 266,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                            lineNumber: 236,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "成员账户管理",
                            description: "在这里您可以管理团队成员的账户状态、权限和角色。只有团队管理员可以进行这些操作。",
                            type: "info",
                            showIcon: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InfoCircleOutlined, {}, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 284,
                                columnNumber: 15
                            }, void 0),
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                            lineNumber: 279,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                justify: "space-between",
                                align: "middle",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "搜索成员姓名或邮箱",
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 294,
                                            columnNumber: 23
                                        }, void 0),
                                        value: searchText,
                                        onChange: (e)=>setSearchText(e.target.value),
                                        style: {
                                            width: 300
                                        },
                                        allowClear: true
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 292,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                    lineNumber: 291,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 290,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                            lineNumber: 289,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                columns: columns,
                                dataSource: filteredMembers,
                                rowKey: "id",
                                loading: loading,
                                pagination: {
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total)=>`共 ${total} 个账户`,
                                    pageSize: 10
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 306,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                            lineNumber: 305,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: `成员设置 - ${selectedMember === null || selectedMember === void 0 ? void 0 : selectedMember.name}`,
                            open: settingsModalVisible,
                            onCancel: ()=>{
                                setSettingsModalVisible(false);
                                settingsForm.resetFields();
                            },
                            footer: null,
                            width: 500,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: settingsForm,
                                layout: "vertical",
                                onFinish: handleSaveSettings,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "role",
                                        label: "角色",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                            disabled: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: "admin",
                                                    children: "管理员"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                    lineNumber: 341,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: "member",
                                                    children: "成员"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                    lineNumber: 342,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 340,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 336,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "isActive",
                                        label: "账户状态",
                                        valuePropName: "checked",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                                            checkedChildren: "活跃",
                                            unCheckedChildren: "停用"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 350,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 345,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    children: "保存设置"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                    lineNumber: 357,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setSettingsModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                                    lineNumber: 360,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                            lineNumber: 356,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                        lineNumber: 355,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                                lineNumber: 331,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                            lineNumber: 321,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/MemberAccountManagement.tsx",
                    lineNumber: 234,
                    columnNumber: 5
                }, this);
            };
            _s(MemberAccountManagement, "fOABXHmL4xgc3a5QsLKerD0AEl8=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = MemberAccountManagement;
            var _default = MemberAccountManagement;
            var _c;
            $RefreshReg$(_c, "MemberAccountManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/.umi/exports.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                Access: function() {
                    return _pluginaccess.Access;
                },
                ApplyPluginsType: function() {
                    return _plugin.ApplyPluginsType;
                },
                Helmet: function() {
                    return _rendererreact.Helmet;
                },
                HelmetProvider: function() {
                    return _rendererreact.HelmetProvider;
                },
                Link: function() {
                    return _rendererreact.Link;
                },
                NavLink: function() {
                    return _rendererreact.NavLink;
                },
                Navigate: function() {
                    return _rendererreact.Navigate;
                },
                Outlet: function() {
                    return _rendererreact.Outlet;
                },
                PluginManager: function() {
                    return _plugin.PluginManager;
                },
                Provider: function() {
                    return _pluginmodel.Provider;
                },
                TestBrowser: function() {
                    return _testBrowser.TestBrowser;
                },
                UseRequestProvider: function() {
                    return _pluginrequest.UseRequestProvider;
                },
                __getRoot: function() {
                    return _rendererreact.__getRoot;
                },
                __useFetcher: function() {
                    return _rendererreact.__useFetcher;
                },
                createBrowserHistory: function() {
                    return _rendererreact.createBrowserHistory;
                },
                createHashHistory: function() {
                    return _rendererreact.createHashHistory;
                },
                createHistory: function() {
                    return _history.createHistory;
                },
                createMemoryHistory: function() {
                    return _rendererreact.createMemoryHistory;
                },
                createSearchParams: function() {
                    return _rendererreact.createSearchParams;
                },
                defineApp: function() {
                    return _defineApp.defineApp;
                },
                generatePath: function() {
                    return _rendererreact.generatePath;
                },
                getRequestInstance: function() {
                    return _pluginrequest.getRequestInstance;
                },
                history: function() {
                    return _history.history;
                },
                matchPath: function() {
                    return _rendererreact.matchPath;
                },
                matchRoutes: function() {
                    return _rendererreact.matchRoutes;
                },
                renderClient: function() {
                    return _rendererreact.renderClient;
                },
                request: function() {
                    return _pluginrequest.request;
                },
                resolvePath: function() {
                    return _rendererreact.resolvePath;
                },
                terminal: function() {
                    return _terminal.terminal;
                },
                useAccess: function() {
                    return _pluginaccess.useAccess;
                },
                useAccessMarkedRoutes: function() {
                    return _pluginaccess.useAccessMarkedRoutes;
                },
                useAntdConfig: function() {
                    return _pluginantd.useAntdConfig;
                },
                useAntdConfigSetter: function() {
                    return _pluginantd.useAntdConfigSetter;
                },
                useAppData: function() {
                    return _rendererreact.useAppData;
                },
                useClientLoaderData: function() {
                    return _rendererreact.useClientLoaderData;
                },
                useLoaderData: function() {
                    return _rendererreact.useLoaderData;
                },
                useLocation: function() {
                    return _rendererreact.useLocation;
                },
                useMatch: function() {
                    return _rendererreact.useMatch;
                },
                useModel: function() {
                    return _pluginmodel.useModel;
                },
                useNavigate: function() {
                    return _rendererreact.useNavigate;
                },
                useOutlet: function() {
                    return _rendererreact.useOutlet;
                },
                useOutletContext: function() {
                    return _rendererreact.useOutletContext;
                },
                useParams: function() {
                    return _rendererreact.useParams;
                },
                useRequest: function() {
                    return _pluginrequest.useRequest;
                },
                useResolvedPath: function() {
                    return _rendererreact.useResolvedPath;
                },
                useRouteData: function() {
                    return _rendererreact.useRouteData;
                },
                useRouteProps: function() {
                    return _rendererreact.useRouteProps;
                },
                useRoutes: function() {
                    return _rendererreact.useRoutes;
                },
                useSearchParams: function() {
                    return _rendererreact.useSearchParams;
                },
                useSelectedRoutes: function() {
                    return _rendererreact.useSelectedRoutes;
                },
                useServerInsertedHTML: function() {
                    return useServerInsertedHTML;
                },
                useServerLoaderData: function() {
                    return _rendererreact.useServerLoaderData;
                },
                withRouter: function() {
                    return _rendererreact.withRouter;
                }
            });
            var _export_star = __mako_require__("@swc/helpers/_/_export_star");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _defineApp = __mako_require__("src/.umi/core/defineApp.ts");
            var _pluginaccess = __mako_require__("src/.umi/plugin-access/index.tsx");
            var _pluginantd = __mako_require__("src/.umi/plugin-antd/index.tsx");
            var _pluginmodel = __mako_require__("src/.umi/plugin-model/index.tsx");
            var _pluginrequest = __mako_require__("src/.umi/plugin-request/index.ts");
            _export_star._(__mako_require__("src/.umi/plugin-access/types.d.ts"), exports);
            _export_star._(__mako_require__("src/.umi/plugin-antd/types.d.ts"), exports);
            _export_star._(__mako_require__("src/.umi/plugin-layout/types.d.ts"), exports);
            _export_star._(__mako_require__("src/.umi/plugin-request/types.d.ts"), exports);
            var _rendererreact = __mako_require__("node_modules/@umijs/renderer-react/dist/index.js");
            var _plugin = __mako_require__("node_modules/umi/client/client/plugin.js");
            var _history = __mako_require__("src/.umi/core/history.ts");
            var _terminal = __mako_require__("src/.umi/core/terminal.ts");
            var _testBrowser = __mako_require__("src/.umi/testBrowser.tsx");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const useServerInsertedHTML = ()=>{};
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15319929890739659627';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=umi.8468081736115640154.hot-update.js.map