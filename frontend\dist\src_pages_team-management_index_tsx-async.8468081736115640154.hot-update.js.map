{"version": 3, "sources": ["src_pages_team-management_index_tsx-async.8468081736115640154.hot-update.js", "src/pages/team-management/index.tsx", "src/pages/team-management/components/TeamMemberManagement.tsx", "src/pages/team-management/components/MemberAccountManagement.tsx", "src/.umi/exports.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/team-management/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15319929890739659627';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 集成团队管理页面\n * \n * 功能特性：\n * - 统一的团队管理界面，包含多个功能模块\n * - 选项卡布局，便于在不同管理功能之间切换\n * - 团队成员管理、账户管理、团队设置等功能\n * - 权限控制，只有团队创建者可以访问管理功能\n * \n * 模块组织：\n * - 团队成员管理：查看、添加、移除团队成员\n * - 成员账户管理：管理成员权限和角色\n * - 团队设置：编辑团队信息、删除团队\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { \n  Tabs, \n  Card, \n  Alert, \n  Spin, \n  Typography, \n  Space,\n  Tag,\n  Avatar,\n  Button\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  SettingOutlined,\n  CrownOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入子组件\nimport TeamMemberManagement from './components/TeamMemberManagement';\nimport MemberAccountManagement from './components/MemberAccountManagement';\nimport TeamSettings from './components/TeamSettings';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst TeamManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [activeTab, setActiveTab] = useState('members');\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      // 如果获取失败，可能是没有选择团队，跳转到团队选择页面\n      history.push('/user/team-select');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 权限检查：只有团队创建者可以访问管理功能\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n          <div style={{ marginTop: 16 }}>\n            <Text type=\"secondary\">正在加载团队信息...</Text>\n          </div>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />\n            <Title level={4}>未找到团队信息</Title>\n            <Text type=\"secondary\">请先选择一个团队</Text>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => history.push('/user/team-select')}>\n                选择团队\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 权限不足提示\n  if (!hasManagePermission) {\n    return (\n      <PageContainer>\n        <Card>\n          <Alert\n            message=\"权限不足\"\n            description=\"只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/dashboard')}>\n                返回首页\n              </Button>\n            }\n          />\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 选项卡配置\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          团队成员管理\n        </Space>\n      ),\n      children: (\n        <TeamMemberManagement \n          teamDetail={teamDetail} \n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n    {\n      key: 'accounts',\n      label: (\n        <Space>\n          <TeamOutlined />\n          成员账户管理\n        </Space>\n      ),\n      children: (\n        <MemberAccountManagement \n          teamDetail={teamDetail} \n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n    {\n      key: 'settings',\n      label: (\n        <Space>\n          <SettingOutlined />\n          团队设置\n        </Space>\n      ),\n      children: (\n        <TeamSettings \n          teamDetail={teamDetail} \n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer\n      title=\"团队管理\"\n      subTitle={\n        <Space>\n          <Avatar size=\"small\" icon={<TeamOutlined />} />\n          <Text>{teamDetail.name}</Text>\n          <Tag icon={<CrownOutlined />} color=\"gold\">管理员</Tag>\n        </Space>\n      }\n      extra={[\n        <Button \n          key=\"back\" \n          onClick={() => history.push('/dashboard')}\n        >\n          返回首页\n        </Button>\n      ]}\n    >\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n          tabBarStyle={{ marginBottom: 24 }}\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n", "/**\n * 团队成员管理组件\n * \n * 功能特性：\n * - 查看团队成员列表及详细信息\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 批量操作支持\n * - 成员搜索和筛选\n * \n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Avatar,\n  Typography,\n  Popconfirm,\n  Select,\n  Divider,\n  Row,\n  Col,\n  Statistic\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  TeamOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchMembers();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      await TeamService.inviteMembers({ emails: emailList });\n      message.success(`成功邀请 ${emailList.length} 名成员`);\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员信息',\n      key: 'member',\n      render: (_, record) => (\n        <Space>\n          <Avatar size=\"large\" icon={<UserOutlined />} />\n          <div>\n            <div>\n              <Space>\n                <Text strong>{record.name}</Text>\n                {record.isCreator && (\n                  <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n                )}\n              </Space>\n            </div>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              {record.email}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'orange'}>\n          {isActive ? '活跃' : '待激活'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Popconfirm\n            title=\"确认移除成员\"\n            description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n            onConfirm={() => handleRemoveMember(record)}\n            okText=\"确认\"\n            cancelText=\"取消\"\n            okType=\"danger\"\n          >\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n            >\n              移除\n            </Button>\n          </Popconfirm>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  return (\n    <div>\n      {/* 统计信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员总数\"\n              value={(members || []).length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃成员\"\n              value={(members || []).filter(m => m.isActive).length}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待激活成员\"\n              value={(members || []).filter(m => !m.isActive).length}\n              prefix={<MailOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={(members || []).filter(m => m.isCreator).length}\n              prefix={<CrownOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 300 }}\n                allowClear\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title=\"批量移除成员\"\n                  description={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确认\"\n                  cancelText=\"取消\"\n                  okType=\"danger\"\n                >\n                  <Button\n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 成员列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n", "/**\n * 成员账户管理组件\n * \n * 功能特性：\n * - 在团队环境中管理成员账户\n * - 处理团队成员的权限和角色\n * - 查看成员账户状态和活动信息\n * - 管理成员的团队访问权限\n * \n * 权限控制：\n * - 只有团队创建者可以进行账户管理操作\n * - 提供详细的权限变更记录\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Tag,\n  Avatar,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Switch,\n  Tooltip,\n  Modal,\n  Form,\n  Select,\n  message,\n  Alert\n} from 'antd';\nimport {\n  UserOutlined,\n  SearchOutlined,\n  CrownOutlined,\n  TeamOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  SettingOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';\n\nconst { Text, Title } = Typography;\n\ninterface MemberAccountManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst MemberAccountManagement: React.FC<MemberAccountManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n  const [selectedMember, setSelectedMember] = useState<TeamMemberResponse | null>(null);\n  const [settingsForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchMembers();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 切换成员活跃状态\n  const handleToggleActiveStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      // 这里应该调用API来更新成员状态\n      // 由于当前后端可能没有这个接口，我们先模拟\n      message.success(`已${isActive ? '激活' : '停用'}成员：${member.name}`);\n      fetchMembers();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 打开成员设置弹窗\n  const handleOpenSettings = (member: TeamMemberResponse) => {\n    setSelectedMember(member);\n    settingsForm.setFieldsValue({\n      role: member.isCreator ? 'admin' : 'member',\n      isActive: member.isActive,\n    });\n    setSettingsModalVisible(true);\n  };\n\n  // 保存成员设置\n  const handleSaveSettings = async (values: any) => {\n    try {\n      // 这里应该调用API来更新成员设置\n      // 由于当前后端可能没有这个接口，我们先模拟\n      message.success(`已更新成员设置：${selectedMember?.name}`);\n      setSettingsModalVisible(false);\n      fetchMembers();\n    } catch (error) {\n      console.error('更新成员设置失败:', error);\n      message.error('更新成员设置失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员信息',\n      key: 'member',\n      render: (_, record) => (\n        <Space>\n          <Avatar size=\"large\" icon={<UserOutlined />} />\n          <div>\n            <div>\n              <Space>\n                <Text strong>{record.name}</Text>\n                {record.isCreator && (\n                  <Tag icon={<CrownOutlined />} color=\"gold\">管理员</Tag>\n                )}\n              </Space>\n            </div>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              {record.email}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      key: 'role',\n      width: 120,\n      render: (_, record) => (\n        <Tag color={record.isCreator ? 'gold' : 'blue'}>\n          {record.isCreator ? '管理员' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '账户状态',\n      key: 'accountStatus',\n      width: 120,\n      render: (_, record) => (\n        <Space>\n          {record.isActive ? (\n            <Tag icon={<CheckCircleOutlined />} color=\"success\">\n              活跃\n            </Tag>\n          ) : (\n            <Tag icon={<CloseCircleOutlined />} color=\"warning\">\n              待激活\n            </Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '权限控制',\n      key: 'permissions',\n      width: 120,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">管理员</Text>;\n        }\n\n        return (\n          <Tooltip title={record.isActive ? '点击停用' : '点击激活'}>\n            <Switch\n              checked={record.isActive}\n              onChange={(checked) => handleToggleActiveStatus(record, checked)}\n              size=\"small\"\n            />\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 100,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Button\n            type=\"text\"\n            size=\"small\"\n            icon={<SettingOutlined />}\n            onClick={() => handleOpenSettings(record)}\n          >\n            设置\n          </Button>\n        );\n      },\n    },\n  ];\n\n  return (\n    <div>\n      {/* 统计信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总成员数\"\n              value={(members || []).length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃账户\"\n              value={(members || []).filter(m => m.isActive).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待激活账户\"\n              value={(members || []).filter(m => !m.isActive).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={(members || []).filter(m => m.isCreator).length}\n              prefix={<CrownOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 功能说明 */}\n      <Alert\n        message=\"成员账户管理\"\n        description=\"在这里您可以管理团队成员的账户状态、权限和角色。只有团队管理员可以进行这些操作。\"\n        type=\"info\"\n        showIcon\n        icon={<InfoCircleOutlined />}\n        style={{ marginBottom: 16 }}\n      />\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Input\n              placeholder=\"搜索成员姓名或邮箱\"\n              prefix={<SearchOutlined />}\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 300 }}\n              allowClear\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 成员账户列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个账户`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 成员设置弹窗 */}\n      <Modal\n        title={`成员设置 - ${selectedMember?.name}`}\n        open={settingsModalVisible}\n        onCancel={() => {\n          setSettingsModalVisible(false);\n          settingsForm.resetFields();\n        }}\n        footer={null}\n        width={500}\n      >\n        <Form\n          form={settingsForm}\n          layout=\"vertical\"\n          onFinish={handleSaveSettings}\n        >\n          <Form.Item\n            name=\"role\"\n            label=\"角色\"\n          >\n            <Select disabled>\n              <Select.Option value=\"admin\">管理员</Select.Option>\n              <Select.Option value=\"member\">成员</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item\n            name=\"isActive\"\n            label=\"账户状态\"\n            valuePropName=\"checked\"\n          >\n            <Switch\n              checkedChildren=\"活跃\"\n              unCheckedChildren=\"停用\"\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                保存设置\n              </Button>\n              <Button onClick={() => setSettingsModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default MemberAccountManagement;\n", "// @ts-nocheck\n// This file is generated by Umi automatically\n// DO NOT CHANGE IT MANUALLY!\n// defineApp\nexport { defineApp } from './core/defineApp'\nexport type { RuntimeConfig } from './core/defineApp'\n// plugins\nexport { Access, useAccess, useAccessMarkedRoutes } from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-access';\nexport { useAntdConfig, useAntdConfigSetter } from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-antd';\nexport { Provider, useModel } from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model';\nexport { useRequest, UseRequestProvider, request, getRequestInstance } from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-request';\n// plugins types.d.ts\nexport * from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-access/types.d';\nexport * from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-antd/types.d';\nexport * from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/types.d';\nexport * from 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-request/types.d';\n// @umijs/renderer-*\nexport { createBrowserHistory, createHashHistory, createMemoryHistory, Helmet, HelmetProvider, createSearchParams, generatePath, matchPath, matchRoutes, Navigate, NavLink, Outlet, resolvePath, useLocation, useMatch, useNavigate, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes, useSearchParams, useAppData, useClientLoaderData, useLoaderData, useRouteProps, useSelectedRoutes, useServerLoaderData, renderClient, __getRoot, Link, useRouteData, __useFetcher, withRouter } from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/renderer-react';\nexport type { History, ClientLoader } from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/renderer-react'\n// umi/client/client/plugin\nexport { ApplyPluginsType, PluginManager } from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/umi/client/client/plugin.js';\nexport { history, createHistory } from './core/history';\nexport { terminal } from './core/terminal';\n// react ssr\nexport const useServerInsertedHTML: Function = () => {};\n// test\nexport { TestBrowser } from './testBrowser';\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC6Mb;;;2BAAA;;;;;;;sEAjM2C;kDACb;yCAWvB;0CAOA;wCACiB;oFAGS;uFACG;4EACX;yCAGG;;;;;;;;;;YAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC,MAAM,qBAA+B;;gBACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;gBACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAE3C,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,kBAAkB;oBACtB,IAAI;wBACF,WAAW;wBACX,MAAM,SAAS,MAAM,iBAAW,CAAC,oBAAoB;wBACrD,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAE3B,YAAO,CAAC,IAAI,CAAC;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAGA,MAAM,sBAAsB,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;gBAErD,IAAI,SACF,OACE,2BAAC,4BAAa;8BACZ,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;;4BACnD,2BAAC,UAAI;gCAAC,MAAK;;;;;;4BACX,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAG;0CAC1B,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;;gBAOjC,IAAI,CAAC,YACH,OACE,2BAAC,4BAAa;8BACZ,2BAAC,UAAI;kCACH,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAS;;gCACnD,2BAAC,yBAAkB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;wCAAW,cAAc;oCAAG;;;;;;gCAC9E,2BAAC;oCAAM,OAAO;8CAAG;;;;;;gCACjB,2BAAC;oCAAK,MAAK;8CAAY;;;;;;gCACvB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAG;8CAC1B,2BAAC,YAAM;wCAAC,MAAK;wCAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWrF,IAAI,CAAC,qBACH,OACE,2BAAC,4BAAa;8BACZ,2BAAC,UAAI;kCACH,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,QACE,2BAAC,YAAM;gCAAC,MAAK;gCAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0CAAe;;;;;;;;;;;;;;;;;;;;;gBAW5E,MAAM,WAAW;oBACf;wBACE,KAAK;wBACL,OACE,2BAAC,WAAK;;gCACJ,2BAAC,mBAAY;;;;;gCAAG;;;;;;;wBAIpB,UACE,2BAAC,6BAAoB;4BACnB,YAAY;4BACZ,WAAW;;;;;;oBAGjB;oBACA;wBACE,KAAK;wBACL,OACE,2BAAC,WAAK;;gCACJ,2BAAC,mBAAY;;;;;gCAAG;;;;;;;wBAIpB,UACE,2BAAC,gCAAuB;4BACtB,YAAY;4BACZ,WAAW;;;;;;oBAGjB;oBACA;wBACE,KAAK;wBACL,OACE,2BAAC,WAAK;;gCACJ,2BAAC,sBAAe;;;;;gCAAG;;;;;;;wBAIvB,UACE,2BAAC,qBAAY;4BACX,YAAY;4BACZ,WAAW;;;;;;oBAGjB;iBACD;gBAED,OACE,2BAAC,4BAAa;oBACZ,OAAM;oBACN,UACE,2BAAC,WAAK;;4BACJ,2BAAC,YAAM;gCAAC,MAAK;gCAAQ,MAAM,2BAAC,mBAAY;;;;;;;;;;4BACxC,2BAAC;0CAAM,WAAW,IAAI;;;;;;4BACtB,2BAAC,SAAG;gCAAC,MAAM,2BAAC,oBAAa;;;;;gCAAK,OAAM;0CAAO;;;;;;;;;;;;oBAG/C,OAAO;wBACL,2BAAC,YAAM;4BAEL,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;sCAC7B;2BAFK;;;;;qBAKP;8BAED,2BAAC,UAAI;kCACH,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU;4BACV,OAAO;4BACP,MAAK;4BACL,aAAa;gCAAE,cAAc;4BAAG;;;;;;;;;;;;;;;;YAK1C;eA9JM;iBAAA;gBAgKN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC8Kf;;;2BAAA;;;;;;sEA9W2C;yCAmBpC;0CASA;yCAIqB;;;;;;;;;;YAG5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;gBACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;gBAEjC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,eAAe;oBACnB,IAAI;wBACF,WAAW;wBACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;wBAC1D,WAAW,cAAc,EAAE;oBAC7B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,WAAW,EAAE;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAGA,MAAM,sBAAsB,OAAO;oBACjC,IAAI;wBACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;wBAEnB,MAAM,iBAAW,CAAC,aAAa,CAAC;4BAAE,QAAQ;wBAAU;wBACpD,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC9C,sBAAsB;wBACtB,WAAW,WAAW;wBACtB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,qBAAqB,OAAO;oBAChC,IAAI;wBACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;wBACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;wBACtC;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,oBAAoB;oBACxB,IAAI;wBACF,MAAM,YAAY;wBAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;wBAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC7C,mBAAmB,EAAE;wBACrB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAI5D,MAAM,UAA2C;oBAC/C;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,SACV,2BAAC,WAAK;;oCACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAQ,MAAM,2BAAC,mBAAY;;;;;;;;;;oCACxC,2BAAC;;4CACC,2BAAC;0DACC,2BAAC,WAAK;;wDACJ,2BAAC;4DAAK,MAAM;sEAAE,OAAO,IAAI;;;;;;wDACxB,OAAO,SAAS,IACf,2BAAC,SAAG;4DAAC,MAAM,2BAAC,oBAAa;;;;;4DAAK,OAAM;sEAAO;;;;;;;;;;;;;;;;;4CAIjD,2BAAC;gDAAK,MAAK;gDAAY,OAAO;oDAAE,UAAU;gDAAG;0DAC1C,OAAO,KAAK;;;;;;;;;;;;;;;;;;oBAKvB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,WACP,2BAAC,SAAG;gCAAC,OAAO,WAAW,UAAU;0CAC9B,WAAW,OAAO;;;;;;oBAGzB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,OAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,OACE,2BAAC,gBAAU;gCACT,OAAM;gCACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;gCAChD,WAAW,IAAM,mBAAmB;gCACpC,QAAO;gCACP,YAAW;gCACX,QAAO;0CAEP,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAM;oCACN,MAAK;oCACL,MAAM,2BAAC,qBAAc;;;;;8CACtB;;;;;;;;;;;wBAKP;oBACF;iBACD;gBAGD,MAAM,eAAe;oBACnB;oBACA,UAAU;oBACV,kBAAkB,CAAC,SAAgC,CAAA;4BACjD,UAAU,OAAO,SAAS;wBAC5B,CAAA;gBACF;gBAEA,OACE,2BAAC;;wBAEC,2BAAC,SAAG;4BAAC,QAAQ;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;gCACzC,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;4CAC7B,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;4CACrD,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;4CACtD,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;4CACtD,QAAQ,2BAAC,oBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;wBAO9B,2BAAC,UAAI;4BAAC,OAAO;gCAAE,cAAc;4BAAG;sCAC9B,2BAAC,SAAG;gCAAC,SAAQ;gCAAgB,OAAM;;oCACjC,2BAAC,SAAG;kDACF,2BAAC,WAAK;;gDACJ,2BAAC,WAAK;oDACJ,aAAY;oDACZ,QAAQ,2BAAC,qBAAc;;;;;oDACvB,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;wDAAE,OAAO;oDAAI;oDACpB,UAAU;;;;;;gDAEX,gBAAgB,MAAM,GAAG,KACxB,2BAAC,gBAAU;oDACT,OAAM;oDACN,aAAa,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;oDAC/D,WAAW;oDACX,QAAO;oDACP,YAAW;oDACX,QAAO;8DAEP,2BAAC,YAAM;wDACL,MAAM;wDACN,MAAM,2BAAC,qBAAc;;;;;;4DACtB;4DACQ,gBAAgB,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;oCAMxC,2BAAC,SAAG;kDACF,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM,2BAAC,sBAAe;;;;;4CACtB,SAAS,IAAM,sBAAsB;sDACtC;;;;;;;;;;;;;;;;;;;;;;wBAQP,2BAAC,UAAI;sCACH,2BAAC,WAAK;gCACJ,SAAS;gCACT,YAAY;gCACZ,QAAO;gCACP,SAAS;gCACT,cAAc;gCACd,YAAY;oCACV,iBAAiB;oCACjB,iBAAiB;oCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oCACtC,UAAU;gCACZ;;;;;;;;;;;wBAKJ,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,sBAAsB;gCACtB,WAAW,WAAW;4BACxB;4BACA,QAAQ;4BACR,OAAO;sCAEP,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;oCAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCACtC;wCACD,OAAM;kDAEN,2BAAC;4CACC,MAAM;4CACN,aAAY;;;;;;;;;;;oCAGhB,2BAAC,UAAI,CAAC,IAAI;kDACR,2BAAC,WAAK;;gDACJ,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,MAAM,2BAAC,mBAAY;;;;;8DAAK;;;;;;gDAGjE,2BAAC,YAAM;oDAAC,SAAS,IAAM,sBAAsB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASnE;eAjUM;;oBASiB,UAAI,CAAC;;;iBATtB;gBAmUN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCCZf;;;2BAAA;;;;;;sEApW2C;yCAoBpC;0CAUA;yCAIqB;;;;;;;;;;YAG5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAOlC,MAAM,0BAAkE,CAAC,EACvE,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAA4B;gBAChF,MAAM,CAAC,aAAa,GAAG,UAAI,CAAC,OAAO;gBAEnC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,eAAe;oBACnB,IAAI;wBACF,WAAW;wBACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;wBAC1D,WAAW,cAAc,EAAE;oBAC7B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,WAAW,EAAE;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAGA,MAAM,2BAA2B,OAAO,QAA4B;oBAClE,IAAI;wBAGF,aAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;wBAC7D;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,qBAAqB,CAAC;oBAC1B,kBAAkB;oBAClB,aAAa,cAAc,CAAC;wBAC1B,MAAM,OAAO,SAAS,GAAG,UAAU;wBACnC,UAAU,OAAO,QAAQ;oBAC3B;oBACA,wBAAwB;gBAC1B;gBAGA,MAAM,qBAAqB,OAAO;oBAChC,IAAI;wBAGF,aAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,2BAAA,qCAAA,eAAgB,IAAI,CAAC,CAAC;wBACjD,wBAAwB;wBACxB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAI5D,MAAM,UAA2C;oBAC/C;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,SACV,2BAAC,WAAK;;oCACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAQ,MAAM,2BAAC,mBAAY;;;;;;;;;;oCACxC,2BAAC;;4CACC,2BAAC;0DACC,2BAAC,WAAK;;wDACJ,2BAAC;4DAAK,MAAM;sEAAE,OAAO,IAAI;;;;;;wDACxB,OAAO,SAAS,IACf,2BAAC,SAAG;4DAAC,MAAM,2BAAC,oBAAa;;;;;4DAAK,OAAM;sEAAO;;;;;;;;;;;;;;;;;4CAIjD,2BAAC;gDAAK,MAAK;gDAAY,OAAO;oDAAE,UAAU;gDAAG;0DAC1C,OAAO,KAAK;;;;;;;;;;;;;;;;;;oBAKvB;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG,SACV,2BAAC,SAAG;gCAAC,OAAO,OAAO,SAAS,GAAG,SAAS;0CACrC,OAAO,SAAS,GAAG,QAAQ;;;;;;oBAGlC;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG,SACV,2BAAC,WAAK;0CACH,OAAO,QAAQ,GACd,2BAAC,SAAG;oCAAC,MAAM,2BAAC,0BAAmB;;;;;oCAAK,OAAM;8CAAU;;;;;2CAIpD,2BAAC,SAAG;oCAAC,MAAM,2BAAC,0BAAmB;;;;;oCAAK,OAAM;8CAAU;;;;;;;;;;;oBAM5D;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,OAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,OACE,2BAAC,aAAO;gCAAC,OAAO,OAAO,QAAQ,GAAG,SAAS;0CACzC,2BAAC,YAAM;oCACL,SAAS,OAAO,QAAQ;oCACxB,UAAU,CAAC,UAAY,yBAAyB,QAAQ;oCACxD,MAAK;;;;;;;;;;;wBAIb;oBACF;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,OAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,OACE,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,MAAM,2BAAC,sBAAe;;;;;gCACtB,SAAS,IAAM,mBAAmB;0CACnC;;;;;;wBAIL;oBACF;iBACD;gBAED,OACE,2BAAC;;wBAEC,2BAAC,SAAG;4BAAC,QAAQ;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;gCACzC,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;4CAC7B,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;4CACrD,QAAQ,2BAAC,0BAAmB;;;;;4CAC5B,YAAY;gDAAE,OAAO;4CAAU;;;;;;;;;;;;;;;;gCAIrC,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;4CACtD,QAAQ,2BAAC,0BAAmB;;;;;4CAC5B,YAAY;gDAAE,OAAO;4CAAU;;;;;;;;;;;;;;;;gCAIrC,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;4CACtD,QAAQ,2BAAC,oBAAa;;;;;4CACtB,YAAY;gDAAE,OAAO;4CAAU;;;;;;;;;;;;;;;;;;;;;;wBAOvC,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,MAAM,2BAAC,yBAAkB;;;;;4BACzB,OAAO;gCAAE,cAAc;4BAAG;;;;;;wBAI5B,2BAAC,UAAI;4BAAC,OAAO;gCAAE,cAAc;4BAAG;sCAC9B,2BAAC,SAAG;gCAAC,SAAQ;gCAAgB,OAAM;0CACjC,2BAAC,SAAG;8CACF,2BAAC,WAAK;wCACJ,aAAY;wCACZ,QAAQ,2BAAC,qBAAc;;;;;wCACvB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;;;;;;;;;;;;;;;;;;;;;wBAOlB,2BAAC,UAAI;sCACH,2BAAC,WAAK;gCACJ,SAAS;gCACT,YAAY;gCACZ,QAAO;gCACP,SAAS;gCACT,YAAY;oCACV,iBAAiB;oCACjB,iBAAiB;oCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oCACtC,UAAU;gCACZ;;;;;;;;;;;wBAKJ,2BAAC,WAAK;4BACJ,OAAO,CAAC,OAAO,EAAE,2BAAA,qCAAA,eAAgB,IAAI,CAAC,CAAC;4BACvC,MAAM;4BACN,UAAU;gCACR,wBAAwB;gCACxB,aAAa,WAAW;4BAC1B;4BACA,QAAQ;4BACR,OAAO;sCAEP,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;oCAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;kDAEN,2BAAC,YAAM;4CAAC,QAAQ;;gDACd,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAM;8DAAQ;;;;;;gDAC7B,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;oCAGlC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,eAAc;kDAEd,2BAAC,YAAM;4CACL,iBAAgB;4CAChB,mBAAkB;;;;;;;;;;;oCAGtB,2BAAC,UAAI,CAAC,IAAI;kDACR,2BAAC,WAAK;;gDACJ,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;8DAAS;;;;;;gDAGzC,2BAAC,YAAM;oDAAC,SAAS,IAAM,wBAAwB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASrE;eAtTM;;oBASmB,UAAI,CAAC;;;iBATxB;gBAwTN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC3WN,MAAM;2BAAN,oBAAM;;gBAaN,gBAAgB;2BAAhB,wBAAgB;;gBAH8C,MAAM;2BAAN,qBAAM;;gBAAE,cAAc;2BAAd,6BAAc;;gBAA6V,IAAI;2BAAJ,mBAAI;;gBAA3R,OAAO;2BAAP,sBAAO;;gBAAjB,QAAQ;2BAAR,uBAAQ;;gBAAW,MAAM;2BAAN,qBAAM;;gBAGvJ,aAAa;2BAAb,qBAAa;;gBAX/B,QAAQ;2BAAR,qBAAQ;;gBAiBR,WAAW;2BAAX,wBAAW;;gBAhBC,kBAAkB;2BAAlB,iCAAkB;;gBAOwY,SAAS;2BAAT,wBAAS;;gBAAsB,YAAY;2BAAZ,2BAAY;;gBAAjd,oBAAoB;2BAApB,mCAAoB;;gBAAE,iBAAiB;2BAAjB,gCAAiB;;gBAI9B,aAAa;2BAAb,sBAAa;;gBAJmB,mBAAmB;2BAAnB,kCAAmB;;gBAA0B,kBAAkB;2BAAlB,iCAAkB;;gBAbxG,SAAS;2BAAT,oBAAS;;gBAaiG,YAAY;2BAAZ,2BAAY;;gBAP7E,kBAAkB;2BAAlB,iCAAkB;;gBAW3D,OAAO;2BAAP,gBAAO;;gBAJiH,SAAS;2BAAT,wBAAS;;gBAAE,WAAW;2BAAX,0BAAW;;gBAA0Q,YAAY;2BAAZ,2BAAY;;gBAPpY,OAAO;2BAAP,sBAAO;;gBAOoI,WAAW;2BAAX,0BAAW;;gBAKtL,QAAQ;2BAAR,kBAAQ;;gBAfA,SAAS;2BAAT,uBAAS;;gBAAE,qBAAqB;2BAArB,mCAAqB;;gBACxC,aAAa;2BAAb,yBAAa;;gBAAE,mBAAmB;2BAAnB,+BAAmB;;gBAS+Q,UAAU;2BAAV,yBAAU;;gBAAE,mBAAmB;2BAAnB,kCAAmB;;gBAAE,aAAa;2BAAb,4BAAa;;gBAAvK,WAAW;2BAAX,0BAAW;;gBAAE,QAAQ;2BAAR,uBAAQ;;gBARnM,QAAQ;2BAAR,qBAAQ;;gBAQ6L,WAAW;2BAAX,0BAAW;;gBAAE,SAAS;2BAAT,wBAAS;;gBAAE,gBAAgB;2BAAhB,+BAAgB;;gBAAE,SAAS;2BAAT,wBAAS;;gBAPlQ,UAAU;2BAAV,yBAAU;;gBAO0P,eAAe;2BAAf,8BAAe;;gBAAoK,YAAY;2BAAZ,2BAAY;;gBAAlG,aAAa;2BAAb,4BAAa;;gBAAzF,SAAS;2BAAT,wBAAS;;gBAAE,eAAe;2BAAf,8BAAe;;gBAAiE,iBAAiB;2BAAjB,gCAAiB;;gBAO7X,qBAAqB;2BAArB;;gBAP+X,mBAAmB;2BAAnB,kCAAmB;;gBAA6D,UAAU;2BAAV,yBAAU;;;;;;8CAb5c;iDAG+B;+CACN;gDAChB;kDACyC;4CAE9D;4CACA;4CACA;4CACA;kDAEge;2CAG9b;4CACT;6CACd;gDAIG;;;;;;;;;YAFrB,MAAM,wBAAkC,KAAO;;;;;;;;;;;;;;;;;;;;;;;IJrBxC;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AAC33B"}