((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__team__detail__index'],
{ "src/pages/team/detail/components/TeamDetailContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队详情组件 - 增强模式显示
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamDetailContent = ({ teamDetail, loading, onRefresh, showBackButton = false, onBack })=>{
    _s();
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [deleting, setDeleting] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    // 辅助函数
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    const getTeamStatusColor = ()=>{
        if (!teamDetail) return '#1890ff';
        const memberCount = teamDetail.memberCount;
        if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃
        if (memberCount >= 5) return '#faad14'; // 橙色 - 正常
        return '#1890ff'; // 蓝色 - 小团队
    };
    const getTeamStatusText = ()=>{
        if (!teamDetail) return '小型团队';
        const memberCount = teamDetail.memberCount;
        if (memberCount >= 10) return '活跃团队';
        if (memberCount >= 5) return '正常团队';
        return '小型团队';
    };
    const handleGoBack = ()=>{
        if (onBack) onBack();
        else _max.history.push('/user/team-select');
    };
    /**
   * 处理编辑团队信息操作
   */ const handleEdit = ()=>{
        if (!teamDetail) return;
        form.setFieldsValue({
            name: teamDetail.name,
            description: teamDetail.description || ''
        });
        setEditModalVisible(true);
    };
    /**
   * 处理团队信息更新操作
   */ const handleUpdateTeam = async (values)=>{
        if (!teamDetail) return;
        try {
            setUpdating(true);
            await _services.TeamService.updateCurrentTeam(values);
            _antd.message.success('团队信息更新成功');
            setEditModalVisible(false);
            onRefresh();
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        } finally{
            setUpdating(false);
        }
    };
    /**
   * 处理删除团队操作
   */ const handleDeleteTeam = ()=>{
        if (!teamDetail) return;
        _antd.Modal.confirm({
            title: '确认删除团队',
            content: `确定要删除团队 "${teamDetail.name}" 吗？此操作不可恢复。`,
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 143,
                columnNumber: 13
            }, this),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    setDeleting(true);
                    await _services.TeamService.deleteCurrentTeam();
                    _antd.message.success('团队删除成功');
                    // 更新全局状态，清除当前团队
                    setInitialState((s)=>({
                            ...s,
                            currentTeam: undefined
                        }));
                    _max.history.push('/user/team-select');
                } catch (error) {
                    console.error('删除团队失败:', error);
                    _antd.message.error('删除团队失败');
                } finally{
                    setDeleting(false);
                }
            }
        });
    };
    // 创建下拉菜单项（增强模式使用）
    const createMenuItems = ()=>[
            {
                key: 'edit',
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 169,
                    columnNumber: 13
                }, this),
                label: '编辑团队',
                onClick: handleEdit
            },
            {
                key: 'delete',
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 175,
                    columnNumber: 13
                }, this),
                label: '删除团队',
                danger: true,
                onClick: handleDeleteTeam
            }
        ];
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            textAlign: 'center',
            padding: '50px 0'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
            size: "large"
        }, void 0, false, {
            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
            lineNumber: 185,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 184,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
        description: "请先选择一个团队"
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 192,
        columnNumber: 7
    }, this);
    // 增强模式渲染
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            padding: '0 24px'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    marginBottom: 24,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    borderRadius: 16
                },
                styles: {
                    body: {
                        padding: '32px'
                    }
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    align: "middle",
                    justify: "space-between",
                    children: [
                        showBackButton && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 218,
                                    columnNumber: 23
                                }, void 0),
                                onClick: handleGoBack,
                                style: {
                                    color: 'rgba(255, 255, 255, 0.8)',
                                    fontSize: 16,
                                    padding: '4px 8px'
                                },
                                children: "返回"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 216,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 215,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            flex: "auto",
                            style: {
                                display: 'flex',
                                justifyContent: 'center',
                                maxWidth: '60%'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                size: "large",
                                align: "center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: 65,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 243,
                                            columnNumber: 23
                                        }, void 0),
                                        style: {
                                            backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                            color: 'white',
                                            fontSize: 28
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 241,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                align: "center",
                                                style: {
                                                    marginBottom: 8
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 2,
                                                        style: {
                                                            color: 'white',
                                                            margin: 0
                                                        },
                                                        children: teamDetail.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 252,
                                                        columnNumber: 19
                                                    }, this),
                                                    teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                            lineNumber: 257,
                                                            columnNumber: 29
                                                        }, void 0),
                                                        color: "gold",
                                                        style: {
                                                            fontSize: 12
                                                        },
                                                        children: "管理员"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 256,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                                        color: getTeamStatusColor(),
                                                        text: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            style: {
                                                                color: 'rgba(255, 255, 255, 0.8)'
                                                            },
                                                            children: getTeamStatusText()
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                            lineNumber: 267,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 264,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                lineNumber: 251,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                style: {
                                                    color: 'rgba(255, 255, 255, 0.8)',
                                                    margin: 0,
                                                    textAlign: 'center'
                                                },
                                                ellipsis: {
                                                    rows: 2
                                                },
                                                children: teamDetail.description || '这个团队还没有描述'
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                lineNumber: 273,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 250,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 240,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 232,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                menu: {
                                    items: createMenuItems()
                                },
                                trigger: [
                                    'click'
                                ],
                                placement: "bottomRight",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 297,
                                        columnNumber: 25
                                    }, void 0),
                                    style: {
                                        color: 'rgba(255, 255, 255, 0.8)',
                                        fontSize: 20,
                                        width: 50,
                                        height: 50
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 295,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 290,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 288,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 212,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 203,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    16,
                    16
                ],
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "团队成员",
                                value: teamDetail.memberCount,
                                suffix: "人",
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                    style: {
                                        color: '#1890ff'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 319,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 315,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 314,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 313,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "创建时间",
                                value: formatDate(teamDetail.createdAt),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                    style: {
                                        color: '#52c41a'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 329,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#52c41a',
                                    fontSize: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 326,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 325,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 324,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "最后活动",
                                value: formatDate(teamDetail.updatedAt),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                    style: {
                                        color: '#faad14'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 339,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#faad14',
                                    fontSize: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 336,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 334,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: 14
                                        },
                                        children: "团队活跃度"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 347,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginTop: 8
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                            type: "circle",
                                            size: 60,
                                            percent: Math.min(teamDetail.memberCount * 10, 100),
                                            strokeColor: getTeamStatusColor(),
                                            format: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 12,
                                                        color: getTeamStatusColor()
                                                    },
                                                    children: teamDetail.memberCount >= 10 ? '高' : teamDetail.memberCount >= 5 ? '中' : '低'
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 357,
                                                    columnNumber: 21
                                                }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 351,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 350,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 346,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 345,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 344,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 312,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                teamId: teamDetail.id,
                isCreator: teamDetail.isCreator,
                onMemberChange: onRefresh
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 373,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>setEditModalVisible(false),
                footer: null,
                width: 600,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleUpdateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    max: 50,
                                    message: '团队名称不能超过50个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 396,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 388,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 403,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 398,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setEditModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 412,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 413,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 411,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 410,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 387,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 380,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 201,
        columnNumber: 5
    }, this);
};
_s(TeamDetailContent, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
    return [
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = TeamDetailContent;
var _default = TeamDetailContent;
var _c;
$RefreshReg$(_c, "TeamDetailContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamMemberList.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员列表组件
 *
 * 功能特性：
 * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）
 * - 支持成员搜索和筛选功能
 * - 提供成员管理操作（移除成员、角色变更等）
 * - 区分创建者和普通成员的权限显示
 * - 响应式表格设计，适配不同屏幕尺寸
 *
 * 权限控制：
 * - 只有团队创建者可以看到管理操作按钮
 * - 创建者不能移除自己
 * - 普通成员只能查看成员列表
 *
 * 交互设计：
 * - 支持批量操作（预留功能）
 * - 提供详细的操作确认对话框
 * - 实时更新成员状态和数量
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
const TeamMemberList = ({ teamId, isCreator, onMemberChange })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [filteredMembers, setFilteredMembers] = (0, _react.useState)([]);
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    const [statusFilter, setStatusFilter] = (0, _react.useState)('all');
    (0, _react.useEffect)(()=>{
        fetchMembers();
    }, [
        teamId
    ]);
    /**
   * 成员列表过滤效果
   *
   * 过滤条件：
   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）
   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员
   *
   * 安全性：
   * - 添加空值检查，防止数据异常导致的错误
   * - 确保成员对象的必要属性存在
   */ (0, _react.useEffect)(()=>{
        // 过滤成员列表 - 添加空值检查
        if (!members || !Array.isArray(members)) {
            setFilteredMembers([]);
            return;
        }
        const filtered = members.filter((member)=>{
            // 确保member对象存在且有必要的属性
            if (!member || !member.name || !member.email) return false;
            // 搜索文本匹配（姓名或邮箱）
            const matchesSearch = !searchText || member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase());
            // 状态筛选匹配
            const matchesStatus = statusFilter === 'all' || statusFilter === 'active' && member.isActive || statusFilter === 'inactive' && !member.isActive || statusFilter === 'creator' && member.isCreator || statusFilter === 'member' && !member.isCreator;
            return matchesSearch && matchesStatus;
        });
        setFilteredMembers(filtered);
    }, [
        members,
        searchText,
        statusFilter
    ]);
    /**
   * 获取团队成员列表
   *
   * 功能：
   * - 调用API获取当前团队的所有成员
   * - 设置加载状态，提供用户反馈
   * - 处理错误情况，确保组件稳定性
   *
   * 数据处理：
   * - 确保返回数据为数组格式，防止渲染错误
   * - 错误时设置空数组，保持组件正常显示
   */ const fetchMembers = async ()=>{
        try {
            setLoading(true);
            const response = await _services.TeamService.getTeamMembers({
                current: 1,
                pageSize: 1000
            });
            // 确保返回的数据是数组格式，防止渲染错误
            setMembers((response === null || response === void 0 ? void 0 : response.list) || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
            // 出错时设置为空数组，保持组件正常显示
            setMembers([]);
        } finally{
            setLoading(false);
        }
    };
    const handleRemoveMember = (member)=>{
        if (member.isCreator) {
            _antd.message.warning('不能移除团队创建者');
            return;
        }
        _antd.Modal.confirm({
            title: '确认移除成员',
            content: `确定要移除成员 "${member.name}" 吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await _services.TeamService.removeMember(member.id);
                    _antd.message.success('成员移除成功');
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('移除成员失败:', error);
                }
            }
        });
    };
    const handleBatchRemove = ()=>{
        const selectedMembers = (members || []).filter((member)=>selectedRowKeys.includes(member.id) && !member.isCreator);
        if (selectedMembers.length === 0) {
            _antd.message.warning('请选择要移除的成员');
            return;
        }
        _antd.Modal.confirm({
            title: '批量移除成员',
            content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await Promise.all(selectedMembers.map((member)=>_services.TeamService.removeMember(member.id)));
                    _antd.message.success(`成功移除 ${selectedMembers.length} 名成员`);
                    setSelectedRowKeys([]);
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('批量移除成员失败:', error);
                    _antd.message.error('批量移除失败');
                }
            }
        });
    };
    const columns = [
        {
            title: '成员',
            dataIndex: 'name',
            key: 'name',
            render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 231,
                                columnNumber: 38
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 231,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: name
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 233,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: 12,
                                        color: '#999'
                                    },
                                    children: record.email
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 234,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 232,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 230,
                    columnNumber: 9
                }, this)
        },
        {
            title: '角色',
            dataIndex: 'isCreator',
            key: 'role',
            width: 100,
            render: (isCreator)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isCreator ? 'gold' : 'blue',
                    icon: isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 247,
                        columnNumber: 29
                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 247,
                        columnNumber: 49
                    }, void 0),
                    children: isCreator ? '创建者' : '成员'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 245,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 80,
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '活跃' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 259,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            width: 150,
            render: (assignedAt)=>new Date(assignedAt).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            width: 150,
            render: (lastAccessTime)=>{
                const date = new Date(lastAccessTime);
                const now = new Date();
                const diffDays = Math.floor((now.getTime() - date.getTime()) / 86400000);
                let color = 'green';
                if (diffDays > 7) color = 'orange';
                if (diffDays > 30) color = 'red';
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: date.toLocaleString(),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                        color: color,
                        children: diffDays === 0 ? '今天' : `${diffDays}天前`
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 289,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 288,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (_, record)=>{
                if (!isCreator || record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 302,
                    columnNumber: 18
                }, this);
                const menuItems = [
                    {
                        key: 'remove',
                        label: '移除成员',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 309,
                            columnNumber: 19
                        }, this),
                        danger: true,
                        onClick: ()=>handleRemoveMember(record)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            danger: true,
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 321,
                                columnNumber: 21
                            }, void 0),
                            onClick: ()=>handleRemoveMember(record),
                            children: "移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 317,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                            menu: {
                                items: menuItems
                            },
                            trigger: [
                                'click'
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.HarmonyOSOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 327,
                                    columnNumber: 54
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 327,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 326,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 316,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys)=>{
            setSelectedRowKeys(newSelectedRowKeys);
        },
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    strong: true,
                    children: "团队成员"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 349,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                    count: filteredMembers.length,
                    showZero: true
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 350,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 348,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                    value: statusFilter,
                    onChange: setStatusFilter,
                    style: {
                        width: 120
                    },
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "all",
                            children: "全部"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 361,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "active",
                            children: "活跃"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 362,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "inactive",
                            children: "停用"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 363,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "creator",
                            children: "创建者"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 364,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "member",
                            children: "成员"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 365,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 355,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                    placeholder: "搜索成员",
                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 369,
                        columnNumber: 21
                    }, void 0),
                    value: searchText,
                    onChange: (e)=>setSearchText(e.target.value),
                    style: {
                        width: 200
                    },
                    size: "small"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 367,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 354,
            columnNumber: 9
        }, void 0),
        children: [
            selectedRowKeys.length > 0 && isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: 12,
                    background: '#f5f5f5',
                    borderRadius: 6
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            children: [
                                "已选择 ",
                                selectedRowKeys.length,
                                " 名成员"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 388,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 392,
                                columnNumber: 21
                            }, void 0),
                            onClick: handleBatchRemove,
                            children: "批量移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 389,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            onClick: ()=>setSelectedRowKeys([]),
                            children: "取消选择"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 397,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 387,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 379,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                columns: columns,
                dataSource: filteredMembers,
                rowKey: "id",
                loading: loading,
                rowSelection: isCreator ? rowSelection : undefined,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 名成员`,
                    pageSize: 10
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 404,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
        lineNumber: 346,
        columnNumber: 5
    }, this);
};
_s(TeamMemberList, "VFoYmIR+E+CHWgWFfALfNS25o10=");
_c = TeamMemberList;
var _default = TeamMemberList;
var _c;
$RefreshReg$(_c, "TeamMemberList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/index.tsx": function (module, exports, __mako_require__){
/**
 * 团队详情页面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _TeamDetailContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamDetailContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const TeamDetailPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
    (0, _react.useEffect)(()=>{
        fetchTeamDetail();
    }, []);
    const fetchTeamDetail = async ()=>{
        try {
            setLoading(true);
            const detail = await _services.TeamService.getCurrentTeamDetail();
            setTeamDetail(detail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
            _antd.message.error('获取团队详情失败');
        } finally{
            setLoading(false);
        }
    };
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '50px 0'
            },
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/team/detail/index.tsx",
                lineNumber: 39,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team/detail/index.tsx",
            lineNumber: 38,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/index.tsx",
        lineNumber: 37,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '50px 0'
            },
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                type: "secondary",
                children: "团队信息加载失败"
            }, void 0, false, {
                fileName: "src/pages/team/detail/index.tsx",
                lineNumber: 49,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team/detail/index.tsx",
            lineNumber: 48,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/index.tsx",
        lineNumber: 47,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        style: {
            background: '#f5f5f5'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamDetailContent.default, {
            teamDetail: teamDetail,
            loading: loading,
            onRefresh: fetchTeamDetail,
            showBackButton: true
        }, void 0, false, {
            fileName: "src/pages/team/detail/index.tsx",
            lineNumber: 57,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/index.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
};
_s(TeamDetailPage, "VLkJGs2Bd2ha+V0CkOUJQHy1qgw=");
_c = TeamDetailPage;
var _default = TeamDetailPage;
var _c;
$RefreshReg$(_c, "TeamDetailPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__team__detail__index-async.js.map