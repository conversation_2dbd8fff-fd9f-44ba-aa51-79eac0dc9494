globalThis.makoModuleHotUpdate('src/pages/team-management/index.tsx', {
    modules: {
        "src/services/team.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TeamService: function() {
                    return TeamService;
                },
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class TeamService {
                static async createTeam(data) {
                    const response = await _request.apiRequest.post('/teams', data);
                    return response.data;
                }
                static async getUserTeams() {
                    const response = await _request.apiRequest.get('/teams');
                    return response.data;
                }
                static async getUserTeamsWithStats() {
                    const response = await _request.apiRequest.get('/teams?includeStats=true');
                    return response.data;
                }
                static async getCurrentTeamDetail() {
                    const response = await _request.apiRequest.get('/teams/current');
                    return response.data;
                }
                static async updateCurrentTeam(data) {
                    const response = await _request.apiRequest.put('/teams/current', data);
                    return response.data;
                }
                static async deleteCurrentTeam() {
                    await _request.apiRequest.delete('/teams/current');
                }
                static async getCurrentTeamMembers() {
                    const response = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    return (response === null || response === void 0 ? void 0 : response.list) || [];
                }
                static async getTeamMembers(params) {
                    const response = await _request.apiRequest.get('/teams/current/members', params);
                    return response.data;
                }
                static async inviteMembers(data) {
                    const response = await _request.apiRequest.post('/teams/current/members/invite', data);
                    return response.data;
                }
                static async removeMember(memberId) {
                    const response = await _request.apiRequest.delete(`/teams/current/members/${memberId}`);
                    return response.data;
                }
                static async checkTeamNameAvailable(name) {
                    try {
                        return true;
                    } catch  {
                        return false;
                    }
                }
                static async getTeamStats() {
                    const teamDetail = await TeamService.getCurrentTeamDetail();
                    const members = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    const activeMembers = members.list.filter((member)=>member.isActive).length;
                    const recentActivity = members.list.filter((member)=>{
                        const lastAccess = new Date(member.lastAccessTime);
                        const weekAgo = new Date();
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return lastAccess > weekAgo;
                    }).length;
                    return {
                        memberCount: teamDetail.memberCount,
                        activeMembers,
                        recentActivity
                    };
                }
                static async searchMembers(keyword) {
                    const members = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    return ((members === null || members === void 0 ? void 0 : members.list) || []).filter((member)=>member.name.toLowerCase().includes(keyword.toLowerCase()) || member.email.toLowerCase().includes(keyword.toLowerCase()));
                }
                static async batchUpdateMemberStatus(memberIds, isActive) {
                    const promises = memberIds.map(async (memberId)=>{});
                    await Promise.all(promises);
                }
            }
            var _default = TeamService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/types/api.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                FriendStatus: function() {
                    return FriendStatus;
                },
                SubscriptionStatus: function() {
                    return SubscriptionStatus;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var SubscriptionStatus;
            (function(SubscriptionStatus) {
                SubscriptionStatus["ACTIVE"] = "ACTIVE";
                SubscriptionStatus["EXPIRED"] = "EXPIRED";
                SubscriptionStatus["CANCELLED"] = "CANCELLED";
            })(SubscriptionStatus || (SubscriptionStatus = {}));
            var FriendStatus;
            (function(FriendStatus) {
                FriendStatus["PENDING"] = "pending";
                FriendStatus["ACCEPTED"] = "accepted";
                FriendStatus["REJECTED"] = "rejected";
            })(FriendStatus || (FriendStatus = {}));
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team-management/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberManagement = _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamMemberManagement.tsx"));
            var _MemberAccountManagement = _interop_require_default._(__mako_require__("src/pages/team-management/components/MemberAccountManagement.tsx"));
            var _TeamSettings = _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamSettings.tsx"));
            var _team = __mako_require__("src/services/team.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const TeamManagementPage = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
                const [activeTab, setActiveTab] = (0, _react.useState)('members');
                (0, _react.useEffect)(()=>{
                    fetchTeamDetail();
                }, []);
                const fetchTeamDetail = async ()=>{
                    try {
                        setLoading(true);
                        const detail = await _team.TeamService.getCurrentTeamDetail();
                        setTeamDetail(detail);
                    } catch (error) {
                        console.error('获取团队详情失败:', error);
                        _max.history.push('/user/team-select');
                    } finally{
                        setLoading(false);
                    }
                };
                const hasManagePermission = (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false;
                if (loading) return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 79,
                                columnNumber: 11
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 16
                                },
                                children: (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "正在加载团队信息..."
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 80,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 77,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                textAlign: 'center',
                                padding: '50px 0'
                            },
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_icons.InfoCircleOutlined, {
                                    style: {
                                        fontSize: 48,
                                        color: '#faad14',
                                        marginBottom: 16
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 93,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    children: "未找到团队信息"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 94,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "请先选择一个团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginTop: 16
                                    },
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        onClick: ()=>_max.history.push('/user/team-select'),
                                        children: "选择团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 97,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 96,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 90,
                    columnNumber: 7
                }, this);
                if (!hasManagePermission) return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "权限不足",
                            description: "只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。",
                            type: "warning",
                            showIcon: true,
                            action: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                size: "small",
                                onClick: ()=>_max.history.push('/dashboard'),
                                children: "返回首页"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 118,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 110,
                    columnNumber: 7
                }, this);
                const tabItems = [
                    {
                        key: 'members',
                        label: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 134,
                                    columnNumber: 11
                                }, this),
                                "团队成员管理"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 133,
                            columnNumber: 9
                        }, this),
                        children: (0, _jsxdevruntime.jsxDEV)(_TeamMemberManagement.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 139,
                            columnNumber: 9
                        }, this)
                    },
                    {
                        key: 'accounts',
                        label: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 149,
                                    columnNumber: 11
                                }, this),
                                "成员账户管理"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 148,
                            columnNumber: 9
                        }, this),
                        children: (0, _jsxdevruntime.jsxDEV)(_MemberAccountManagement.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 154,
                            columnNumber: 9
                        }, this)
                    },
                    {
                        key: 'settings',
                        label: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 164,
                                    columnNumber: 11
                                }, this),
                                "团队设置"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 163,
                            columnNumber: 9
                        }, this),
                        children: (0, _jsxdevruntime.jsxDEV)(_TeamSettings.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 169,
                            columnNumber: 9
                        }, this)
                    }
                ];
                return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    title: "团队管理",
                    subTitle: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                size: "small",
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 182,
                                    columnNumber: 38
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 182,
                                columnNumber: 11
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: teamDetail.name
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 183,
                                columnNumber: 11
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 184,
                                    columnNumber: 22
                                }, void 0),
                                color: "gold",
                                children: "管理员"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 184,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 181,
                        columnNumber: 9
                    }, void 0),
                    extra: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            onClick: ()=>_max.history.push('/dashboard'),
                            children: "返回首页"
                        }, "back", false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 188,
                            columnNumber: 9
                        }, void 0)
                    ],
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: setActiveTab,
                            items: tabItems,
                            size: "large",
                            tabBarStyle: {
                                marginBottom: 24
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 197,
                            columnNumber: 9
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 196,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 178,
                    columnNumber: 5
                }, this);
            };
            _s(TeamManagementPage, "MbpIoVQJb3zY91BHiEpQzi7JPmo=");
            _c = TeamManagementPage;
            var _default = TeamManagementPage;
            var _c;
            $RefreshReg$(_c, "TeamManagementPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team-management/components/TeamMemberManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _team = __mako_require__("src/services/team.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
                const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
                const [inviteForm] = _antd.Form.useForm();
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        _antd.message.error('获取团队成员失败');
                        setMembers([]);
                    } finally{
                        setLoading(false);
                    }
                };
                const handleInviteMembers = async (values)=>{
                    try {
                        const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
                        await _team.TeamService.inviteMembers({
                            emails: emailList
                        });
                        _antd.message.success(`成功邀请 ${emailList.length} 名成员`);
                        setInviteModalVisible(false);
                        inviteForm.resetFields();
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('邀请成员失败:', error);
                        _antd.message.error('邀请成员失败');
                    }
                };
                const handleRemoveMember = async (member)=>{
                    try {
                        await _team.TeamService.removeMember(member.id);
                        _antd.message.success(`已移除成员：${member.name}`);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('移除成员失败:', error);
                        _antd.message.error('移除成员失败');
                    }
                };
                const handleBatchRemove = async ()=>{
                    try {
                        const memberIds = selectedRowKeys;
                        for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
                        _antd.message.success(`已移除 ${memberIds.length} 名成员`);
                        setSelectedRowKeys([]);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('批量移除成员失败:', error);
                        _antd.message.error('批量移除成员失败');
                    }
                };
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                const columns = [
                    {
                        title: '成员信息',
                        key: 'member',
                        render: (_, record)=>(0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: "large",
                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 152,
                                            columnNumber: 38
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 152,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)("div", {
                                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: record.name
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 156,
                                                            columnNumber: 17
                                                        }, this),
                                                        record.isCreator && (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 158,
                                                                columnNumber: 30
                                                            }, void 0),
                                                            color: "gold",
                                                            children: "创建者"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 158,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 155,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 154,
                                                columnNumber: 13
                                            }, this),
                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: 12
                                                },
                                                children: record.email
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 162,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 153,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 151,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'status',
                        width: 100,
                        render: (isActive)=>(0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: isActive ? 'green' : 'orange',
                                children: isActive ? '活跃' : '待激活'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 175,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '加入时间',
                        dataIndex: 'assignedAt',
                        key: 'assignedAt',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 120,
                        render: (_, record)=>{
                            if (record.isCreator) return (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 200,
                                columnNumber: 18
                            }, this);
                            return (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                title: "确认移除成员",
                                description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                                onConfirm: ()=>handleRemoveMember(record),
                                okText: "确认",
                                cancelText: "取消",
                                okType: "danger",
                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    danger: true,
                                    size: "small",
                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 216,
                                        columnNumber: 21
                                    }, void 0),
                                    children: "移除"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 212,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 204,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                const rowSelection = {
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record)=>({
                            disabled: record.isCreator
                        })
                };
                return (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: 16,
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员总数",
                                            value: (members || []).length,
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 244,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 241,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 240,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 239,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "活跃成员",
                                            value: (members || []).filter((m)=>m.isActive).length,
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 253,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 250,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 249,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 248,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "待激活成员",
                                            value: (members || []).filter((m)=>!m.isActive).length,
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 262,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 259,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 258,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 257,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "管理员",
                                            value: (members || []).filter((m)=>m.isCreator).length,
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 271,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 268,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 267,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 266,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 238,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 16
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                justify: "space-between",
                                align: "middle",
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "搜索成员姓名或邮箱",
                                                    prefix: (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 284,
                                                        columnNumber: 25
                                                    }, void 0),
                                                    value: searchText,
                                                    onChange: (e)=>setSearchText(e.target.value),
                                                    style: {
                                                        width: 300
                                                    },
                                                    allowClear: true
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 282,
                                                    columnNumber: 15
                                                }, this),
                                                selectedRowKeys.length > 0 && (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                    title: "批量移除成员",
                                                    description: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`,
                                                    onConfirm: handleBatchRemove,
                                                    okText: "确认",
                                                    cancelText: "取消",
                                                    okType: "danger",
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        danger: true,
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 301,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        children: [
                                                            "批量移除 (",
                                                            selectedRowKeys.length,
                                                            ")"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 299,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 291,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 281,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 280,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 312,
                                                columnNumber: 21
                                            }, void 0),
                                            onClick: ()=>setInviteModalVisible(true),
                                            children: "邀请成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 310,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 309,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 279,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 278,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                columns: columns,
                                dataSource: filteredMembers,
                                rowKey: "id",
                                loading: loading,
                                rowSelection: rowSelection,
                                pagination: {
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total)=>`共 ${total} 名成员`,
                                    pageSize: 10
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 323,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 322,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "邀请新成员",
                            open: inviteModalVisible,
                            onCancel: ()=>{
                                setInviteModalVisible(false);
                                inviteForm.resetFields();
                            },
                            footer: null,
                            width: 600,
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: inviteForm,
                                layout: "vertical",
                                onFinish: handleInviteMembers,
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "emails",
                                        label: "邮箱地址",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱地址'
                                            }
                                        ],
                                        extra: "每行一个邮箱地址，支持批量邀请",
                                        children: (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 6,
                                            placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 362,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 354,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 369,
                                                        columnNumber: 62
                                                    }, void 0),
                                                    children: "发送邀请"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 369,
                                                    columnNumber: 15
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setInviteModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 372,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 368,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 367,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 349,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 339,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 236,
                    columnNumber: 5
                }, this);
            };
            _s(TeamMemberManagement, "lwyw8TlHxmNVMk/bokFh1nGrhdE=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TeamMemberManagement;
            var _default = TeamMemberManagement;
            var _c;
            $RefreshReg$(_c, "TeamMemberManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15034079070642284381';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_team-management_index_tsx-async.15319929890739659627.hot-update.js.map