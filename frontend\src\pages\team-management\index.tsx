/**
 * 集成团队管理页面
 * 
 * 功能特性：
 * - 统一的团队管理界面，包含多个功能模块
 * - 选项卡布局，便于在不同管理功能之间切换
 * - 团队成员管理、账户管理、团队设置等功能
 * - 权限控制，只有团队创建者可以访问管理功能
 * 
 * 模块组织：
 * - 团队成员管理：查看、添加、移除团队成员
 * - 成员账户管理：管理成员权限和角色
 * - 团队设置：编辑团队信息、删除团队
 */

import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { 
  Tabs, 
  Card, 
  Alert, 
  Spin, 
  Typography, 
  Space,
  Tag,
  Avatar,
  Button
} from 'antd';
import { 
  TeamOutlined, 
  UserOutlined, 
  SettingOutlined,
  CrownOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { history } from '@umijs/max';

// 导入子组件
import TeamMemberManagement from './components/TeamMemberManagement';
import TeamSettings from './components/TeamSettings';

// 导入服务和类型
import { TeamService } from '@/services/team';
import type { TeamDetailResponse } from '@/types/api';

const { Title, Text } = Typography;

const TeamManagementPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);
  const [activeTab, setActiveTab] = useState('members');

  useEffect(() => {
    fetchTeamDetail();
  }, []);

  const fetchTeamDetail = async () => {
    try {
      setLoading(true);
      const detail = await TeamService.getCurrentTeamDetail();
      setTeamDetail(detail);
    } catch (error) {
      console.error('获取团队详情失败:', error);
      // 如果获取失败，可能是没有选择团队，跳转到团队选择页面
      history.push('/user/team-select');
    } finally {
      setLoading(false);
    }
  };

  // 权限检查：只有团队创建者可以访问管理功能
  const hasManagePermission = teamDetail?.isCreator || false;

  if (loading) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">正在加载团队信息...</Text>
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!teamDetail) {
    return (
      <PageContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
            <Title level={4}>未找到团队信息</Title>
            <Text type="secondary">请先选择一个团队</Text>
            <div style={{ marginTop: 16 }}>
              <Button type="primary" onClick={() => history.push('/user/team-select')}>
                选择团队
              </Button>
            </div>
          </div>
        </Card>
      </PageContainer>
    );
  }

  // 权限不足提示
  if (!hasManagePermission) {
    return (
      <PageContainer>
        <Card>
          <Alert
            message="权限不足"
            description="只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。"
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={() => history.push('/dashboard')}>
                返回首页
              </Button>
            }
          />
        </Card>
      </PageContainer>
    );
  }

  // 选项卡配置
  const tabItems = [
    {
      key: 'members',
      label: (
        <Space>
          <UserOutlined />
          团队成员管理
        </Space>
      ),
      children: (
        <TeamMemberManagement
          teamDetail={teamDetail}
          onRefresh={fetchTeamDetail}
        />
      ),
    },
    {
      key: 'settings',
      label: (
        <Space>
          <SettingOutlined />
          团队设置
        </Space>
      ),
      children: (
        <TeamSettings
          teamDetail={teamDetail}
          onRefresh={fetchTeamDetail}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>
    </PageContainer>
  );
};

export default TeamManagementPage;
