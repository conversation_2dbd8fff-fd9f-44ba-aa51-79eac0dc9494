{"version": 3, "sources": ["src_pages_team-management_index_tsx-async.15319929890739659627.hot-update.js", "src/services/team.ts", "src/types/api.ts", "src/pages/team-management/index.tsx", "src/pages/team-management/components/TeamMemberManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/team-management/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15034079070642284381';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队管理相关 API 服务\n */\n\nimport type {\n  CreateTeamRequest,\n  InviteMembersRequest,\n  PageRequest,\n  PageResponse,\n  TeamDetailResponse,\n  TeamMemberResponse,\n  TeamStatsData,\n  UpdateTeamRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 团队服务类\n */\nexport class TeamService {\n  /**\n   * 创建团队（需要 Account Token）\n   */\n  static async createTeam(\n    data: CreateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（需要 Account Token）\n   */\n  static async getUserTeams(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（包含统计数据）\n   */\n  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>(\n      '/teams?includeStats=true',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取当前团队详情（需要 Team Token）\n   */\n  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {\n    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');\n    return response.data;\n  }\n\n  /**\n   * 更新当前团队信息（需要 Team Token，仅创建者）\n   */\n  static async updateCurrentTeam(\n    data: UpdateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.put<TeamDetailResponse>(\n      '/teams/current',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除当前团队（需要 Team Token，仅创建者）\n   *\n   * 权限要求：\n   * - 需要有效的Team Token\n   * - 只有团队创建者可以执行此操作\n   *\n   * 删除效果：\n   * - 软删除团队记录\n   * - 级联删除所有团队成员关系\n   * - 不可恢复\n   *\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteCurrentTeam(): Promise<void> {\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 获取当前团队成员列表（需要 Team Token）\n   * 返回所有成员的简单数组格式\n   */\n  static async getCurrentTeamMembers(): Promise<TeamMemberResponse[]> {\n    const response = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000, // 获取大量数据以确保包含所有成员\n    });\n    return response?.list || [];\n  }\n\n  /**\n   * 获取当前团队成员列表（需要 Team Token）\n   */\n  static async getTeamMembers(\n    params?: PageRequest,\n  ): Promise<PageResponse<TeamMemberResponse>> {\n    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(\n      '/teams/current/members',\n      params,\n    );\n    return response.data;\n  }\n\n  /**\n   * 邀请团队成员（需要 Team Token，仅创建者）\n   */\n  static async inviteMembers(data: InviteMembersRequest): Promise<void> {\n    const response = await apiRequest.post<void>(\n      '/teams/current/members/invite',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 移除团队成员（需要 Team Token，仅创建者）\n   */\n  static async removeMember(memberId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(\n      `/teams/current/members/${memberId}`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 检查团队名称是否可用\n   */\n  static async checkTeamNameAvailable(name: string): Promise<boolean> {\n    try {\n      // 这里可能需要后端提供专门的检查接口\n      // 暂时通过创建团队的错误响应来判断\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 获取团队统计信息\n   */\n  static async getTeamStats(): Promise<{\n    memberCount: number;\n    activeMembers: number;\n    recentActivity: number;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时通过团队详情和成员列表来计算\n    const teamDetail = await TeamService.getCurrentTeamDetail();\n    const members = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000,\n    });\n\n    const activeMembers = members.list.filter(\n      (member) => member.isActive,\n    ).length;\n    const recentActivity = members.list.filter((member) => {\n      const lastAccess = new Date(member.lastAccessTime);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return lastAccess > weekAgo;\n    }).length;\n\n    return {\n      memberCount: teamDetail.memberCount,\n      activeMembers,\n      recentActivity,\n    };\n  }\n\n  /**\n   * 搜索团队成员\n   */\n  static async searchMembers(keyword: string): Promise<TeamMemberResponse[]> {\n    const members = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000,\n    });\n\n    // 前端过滤，后端可以提供专门的搜索接口\n    return (members?.list || []).filter(\n      (member) =>\n        member.name.toLowerCase().includes(keyword.toLowerCase()) ||\n        member.email.toLowerCase().includes(keyword.toLowerCase()),\n    );\n  }\n\n  /**\n   * 批量操作成员状态\n   */\n  static async batchUpdateMemberStatus(\n    memberIds: number[],\n    isActive: boolean,\n  ): Promise<void> {\n    // 这里需要后端提供批量操作接口\n    // 暂时使用循环调用单个接口\n    const promises = memberIds.map(async (memberId) => {\n      // 假设有单个更新成员状态的接口\n      // await this.updateMemberStatus(memberId, isActive);\n    });\n\n    await Promise.all(promises);\n  }\n}\n\n// 导出默认实例\nexport default TeamService;\n", "/**\n * API 相关类型定义\n * 基于后端 DTO 和 Entity 类生成\n */\n\n// ============= 基础类型 =============\n\n/**\n * API 响应基础结构\n */\nexport interface ApiResponse<T = any> {\n  code: number;\n  message: string;\n  data: T;\n  timestamp: string;\n}\n\n/**\n * 分页请求参数\n */\nexport interface PageRequest {\n  current?: number;\n  pageSize?: number;\n}\n\n/**\n * 分页响应结构\n */\nexport interface PageResponse<T> {\n  list: T[];\n  total: number;\n  current: number;\n  pageSize: number;\n}\n\n// ============= 认证相关类型 =============\n\n/**\n * 登录请求\n */\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\n/**\n * 注册请求\n */\nexport interface RegisterRequest {\n  email: string;\n  password: string;\n  name: string;\n}\n\n/**\n * 用户信息\n */\nexport interface UserInfo {\n  id: number;\n  email: string;\n  name: string;\n}\n\n/**\n * 团队信息（登录响应中的简化版本）\n */\nexport interface TeamInfo {\n  id: number;\n  name: string;\n  isCreator: boolean;\n  memberCount: number;\n  lastAccessTime: string;\n}\n\n/**\n * 登录响应\n */\nexport interface LoginResponse {\n  token: string;\n  expiresIn: number;\n  user: UserInfo;\n  teams: TeamInfo[];\n  /** 当前选择的团队信息（用于团队选择响应） */\n  team?: TeamInfo;\n  /** 团队选择成功标识（用于团队选择响应） */\n  teamSelectionSuccess?: boolean;\n}\n\n// ============= 团队管理相关类型 =============\n\n/**\n * 创建团队请求\n */\nexport interface CreateTeamRequest {\n  name: string;\n  description?: string;\n}\n\n/**\n * 更新团队请求\n */\nexport interface UpdateTeamRequest {\n  name: string;\n  description?: string;\n}\n\n/**\n * 邀请成员请求\n */\nexport interface InviteMembersRequest {\n  emails: string[];\n}\n\n/**\n * 团队详情响应\n */\nexport interface TeamDetailResponse {\n  id: number;\n  name: string;\n  description?: string;\n  createdBy: number;\n  memberCount: number;\n  isCreator: boolean;\n  createdAt: string;\n  updatedAt: string;\n  stats?: TeamStatsData; // 可选的统计数据\n}\n\n/**\n * 团队成员响应\n */\nexport interface TeamMemberResponse {\n  id: number;\n  accountId: number;\n  email: string;\n  name: string;\n  isCreator: boolean;\n  assignedAt: string;\n  lastAccessTime: string;\n  isActive: boolean;\n}\n\n// ============= 用户管理相关类型 =============\n\n/**\n * 用户资料响应\n */\nexport interface UserProfileResponse {\n  id: number;\n  email: string;\n  name: string;\n  telephone?: string;\n  defaultSubscriptionPlanId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 更新用户资料请求\n */\nexport interface UpdateUserProfileRequest {\n  name?: string;\n  telephone?: string;\n  currentPassword?: string;\n  newPassword?: string;\n}\n\n// ============= 订阅管理相关类型 =============\n\n/**\n * 订阅套餐响应\n */\nexport interface SubscriptionPlanResponse {\n  id: number;\n  name: string;\n  description: string;\n  maxSize: number;\n  price: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 创建订阅请求\n */\nexport interface CreateSubscriptionRequest {\n  planId: number;\n  duration: number;\n}\n\n/**\n * 订阅状态枚举\n */\nexport enum SubscriptionStatus {\n  ACTIVE = 'ACTIVE',\n  EXPIRED = 'EXPIRED',\n  CANCELLED = 'CANCELLED',\n}\n\n/**\n * 订阅响应\n */\nexport interface SubscriptionResponse {\n  id: number;\n  accountId: number;\n  subscriptionPlanId: number;\n  planName: string;\n  planDescription: string;\n  maxSize: number;\n  price: number;\n  startDate: string;\n  endDate: string;\n  status: SubscriptionStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// ============= 实体类型 =============\n\n/**\n * 账户实体\n */\nexport interface Account {\n  id: number;\n  email: string;\n  name: string;\n  defaultSubscriptionPlanId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 包含备注信息的好友响应\n */\nexport interface FriendWithRemark {\n  id: number;\n  email: string;\n  name: string;\n  remark?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 团队实体\n */\nexport interface Team {\n  id: number;\n  name: string;\n  description?: string;\n  createdBy: number;\n  isDeleted: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 团队成员实体\n */\nexport interface TeamMember {\n  id: number;\n  teamId: number;\n  accountId: number;\n  isCreator: boolean;\n  assignedAt: string;\n  lastAccessTime: string;\n  isActive: boolean;\n  isDeleted: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 订阅套餐实体\n */\nexport interface SubscriptionPlan {\n  id: number;\n  name: string;\n  description: string;\n  maxSize: number;\n  price: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 账户订阅实体\n */\nexport interface AccountSubscription {\n  id: number;\n  accountId: number;\n  subscriptionPlanId: number;\n  startDate: string;\n  endDate: string;\n  status: SubscriptionStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 好友关系状态枚举\n */\nexport enum FriendStatus {\n  PENDING = 'pending',\n  ACCEPTED = 'accepted',\n  REJECTED = 'rejected',\n}\n\n/**\n * 账户关系实体\n */\nexport interface AccountRelation {\n  id: number;\n  accountId: number;\n  invitedBy: number;\n  invitedAt: string;\n  remark?: string;\n  status: FriendStatus;\n  requestedAt: string;\n  isActive: boolean;\n  isDeleted: boolean;\n  updatedAt: string;\n}\n\n// ============= 好友管理相关类型 =============\n\n/**\n * 添加好友请求\n */\nexport interface AddFriendRequest {\n  email: string;\n}\n\n/**\n * 设置好友备注请求\n */\nexport interface SetFriendRemarkRequest {\n  friendId: number;\n  remark: string;\n}\n\n/**\n * 从好友列表邀请成员请求\n */\nexport interface InviteFriendsRequest {\n  friendIds: number[];\n}\n\n// ============= 用户统计相关类型 =============\n\n/**\n * 用户个人统计数据响应\n */\nexport interface UserPersonalStatsResponse {\n  vehicles: number;\n  personnel: number;\n  warnings: number;\n  alerts: number;\n}\n\n/**\n * 团队统计数据\n */\nexport interface TeamStatsData {\n  vehicles: number;\n  personnel: number;\n  expiring: number;\n  overdue: number;\n}\n\n/**\n * 用户详细信息响应\n */\nexport interface UserProfileDetailResponse {\n  name: string;\n  position: string;\n  email: string;\n  telephone: string;\n  registerDate: string;\n  lastLoginTime: string;\n  lastLoginTeam: string;\n  teamCount: number;\n  avatar?: string;\n}\n\n// ============= TODO相关类型 =============\n\n/**\n * TODO响应\n */\nexport interface TodoResponse {\n  id: number;\n  title: string;\n  description?: string;\n  status: number; // 0-未完成，1-已完成\n  priority: number; // 1-低，2-中，3-高\n  userId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 创建TODO请求\n */\nexport interface CreateTodoRequest {\n  title: string;\n  description?: string;\n  priority: number;\n}\n\n/**\n * 更新TODO请求\n */\nexport interface UpdateTodoRequest {\n  title?: string;\n  description?: string;\n  status?: number;\n  priority?: number;\n}\n\n/**\n * TODO统计信息响应\n */\nexport interface TodoStatsResponse {\n  highPriorityCount: number;\n  mediumPriorityCount: number;\n  lowPriorityCount: number;\n  totalCount: number;\n  completedCount: number;\n  completionPercentage: number;\n}\n", "/**\n * 集成团队管理页面\n * \n * 功能特性：\n * - 统一的团队管理界面，包含多个功能模块\n * - 选项卡布局，便于在不同管理功能之间切换\n * - 团队成员管理、账户管理、团队设置等功能\n * - 权限控制，只有团队创建者可以访问管理功能\n * \n * 模块组织：\n * - 团队成员管理：查看、添加、移除团队成员\n * - 成员账户管理：管理成员权限和角色\n * - 团队设置：编辑团队信息、删除团队\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { \n  Tabs, \n  Card, \n  Alert, \n  Spin, \n  Typography, \n  Space,\n  Tag,\n  Avatar,\n  Button\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  SettingOutlined,\n  CrownOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入子组件\nimport TeamMemberManagement from './components/TeamMemberManagement';\nimport MemberAccountManagement from './components/MemberAccountManagement';\nimport TeamSettings from './components/TeamSettings';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst TeamManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [activeTab, setActiveTab] = useState('members');\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      // 如果获取失败，可能是没有选择团队，跳转到团队选择页面\n      history.push('/user/team-select');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 权限检查：只有团队创建者可以访问管理功能\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n          <div style={{ marginTop: 16 }}>\n            <Text type=\"secondary\">正在加载团队信息...</Text>\n          </div>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />\n            <Title level={4}>未找到团队信息</Title>\n            <Text type=\"secondary\">请先选择一个团队</Text>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => history.push('/user/team-select')}>\n                选择团队\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 权限不足提示\n  if (!hasManagePermission) {\n    return (\n      <PageContainer>\n        <Card>\n          <Alert\n            message=\"权限不足\"\n            description=\"只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/dashboard')}>\n                返回首页\n              </Button>\n            }\n          />\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 选项卡配置\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          团队成员管理\n        </Space>\n      ),\n      children: (\n        <TeamMemberManagement \n          teamDetail={teamDetail} \n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n    {\n      key: 'accounts',\n      label: (\n        <Space>\n          <TeamOutlined />\n          成员账户管理\n        </Space>\n      ),\n      children: (\n        <MemberAccountManagement \n          teamDetail={teamDetail} \n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n    {\n      key: 'settings',\n      label: (\n        <Space>\n          <SettingOutlined />\n          团队设置\n        </Space>\n      ),\n      children: (\n        <TeamSettings \n          teamDetail={teamDetail} \n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer\n      title=\"团队管理\"\n      subTitle={\n        <Space>\n          <Avatar size=\"small\" icon={<TeamOutlined />} />\n          <Text>{teamDetail.name}</Text>\n          <Tag icon={<CrownOutlined />} color=\"gold\">管理员</Tag>\n        </Space>\n      }\n      extra={[\n        <Button \n          key=\"back\" \n          onClick={() => history.push('/dashboard')}\n        >\n          返回首页\n        </Button>\n      ]}\n    >\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n          tabBarStyle={{ marginBottom: 24 }}\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n", "/**\n * 团队成员管理组件\n * \n * 功能特性：\n * - 查看团队成员列表及详细信息\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 批量操作支持\n * - 成员搜索和筛选\n * \n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Avatar,\n  Typography,\n  Popconfirm,\n  Select,\n  Divider,\n  Row,\n  Col,\n  Statistic\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  TeamOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchMembers();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      await TeamService.inviteMembers({ emails: emailList });\n      message.success(`成功邀请 ${emailList.length} 名成员`);\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员信息',\n      key: 'member',\n      render: (_, record) => (\n        <Space>\n          <Avatar size=\"large\" icon={<UserOutlined />} />\n          <div>\n            <div>\n              <Space>\n                <Text strong>{record.name}</Text>\n                {record.isCreator && (\n                  <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n                )}\n              </Space>\n            </div>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              {record.email}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'orange'}>\n          {isActive ? '活跃' : '待激活'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Popconfirm\n            title=\"确认移除成员\"\n            description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n            onConfirm={() => handleRemoveMember(record)}\n            okText=\"确认\"\n            cancelText=\"取消\"\n            okType=\"danger\"\n          >\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n            >\n              移除\n            </Button>\n          </Popconfirm>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  return (\n    <div>\n      {/* 统计信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员总数\"\n              value={(members || []).length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃成员\"\n              value={(members || []).filter(m => m.isActive).length}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待激活成员\"\n              value={(members || []).filter(m => !m.isActive).length}\n              prefix={<MailOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={(members || []).filter(m => m.isCreator).length}\n              prefix={<CrownOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 300 }}\n                allowClear\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title=\"批量移除成员\"\n                  description={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确认\"\n                  cancelText=\"取消\"\n                  okType=\"danger\"\n                >\n                  <Button\n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 成员列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;;;;;;;;gBCgBA,WAAW;2BAAX;;gBAqMb,OAA2B;2BAA3B;;;;;4CA1M2B;;;;;;;;;YAKpB,MAAM;gBAIX,aAAa,WACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAqB,UAAU;oBACrE,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,eAA8C;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,uBAAoD;oBAC/D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB;oBAC1D,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,kBACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAiBA,aAAa,oBAAmC;oBAC9C,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBAMA,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,YAAY,cAAc,CAAC;wBAChD,SAAS;wBACT,UAAU;oBACZ;oBACA,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;gBAC7B;gBAKA,aAAa,eACX,MAAoB,EACuB;oBAC3C,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,0BACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,cAAc,IAA0B,EAAiB;oBACpE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,iCACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,aAAa,QAAgB,EAAiB;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CACtC,CAAC,uBAAuB,EAAE,SAAS,CAAC;oBAEtC,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,uBAAuB,IAAY,EAAoB;oBAClE,IAAI;wBAGF,OAAO;oBACT,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAKA,aAAa,eAIV;oBAGD,MAAM,aAAa,MAAM,YAAY,oBAAoB;oBACzD,MAAM,UAAU,MAAM,YAAY,cAAc,CAAC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;oBAEA,MAAM,gBAAgB,QAAQ,IAAI,CAAC,MAAM,CACvC,CAAC,SAAW,OAAO,QAAQ,EAC3B,MAAM;oBACR,MAAM,iBAAiB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1C,MAAM,aAAa,IAAI,KAAK,OAAO,cAAc;wBACjD,MAAM,UAAU,IAAI;wBACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;wBACpC,OAAO,aAAa;oBACtB,GAAG,MAAM;oBAET,OAAO;wBACL,aAAa,WAAW,WAAW;wBACnC;wBACA;oBACF;gBACF;gBAKA,aAAa,cAAc,OAAe,EAAiC;oBACzE,MAAM,UAAU,MAAM,YAAY,cAAc,CAAC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;oBAGA,OAAO,AAAC,CAAA,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,EAAE,AAAD,EAAG,MAAM,CACjC,CAAC,SACC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,OACtD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;gBAE7D;gBAKA,aAAa,wBACX,SAAmB,EACnB,QAAiB,EACF;oBAGf,MAAM,WAAW,UAAU,GAAG,CAAC,OAAO,YAGtC;oBAEA,MAAM,QAAQ,GAAG,CAAC;gBACpB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBCtBH;;;;eAAA,uBAAA;;sBA8GA;;;;eAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCChGZ;;;2BAAA;;;;;;;sEAjM2C;kDACb;yCAWvB;0CAOA;wCACiB;oFAGS;uFACG;4EACX;yCAGG;;;;;;;;;;YAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC,MAAM,qBAA+B;;gBACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;gBACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAE3C,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,kBAAkB;oBACtB,IAAI;wBACF,WAAW;wBACX,MAAM,SAAS,MAAM,iBAAW,CAAC,oBAAoB;wBACrD,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAE3B,YAAO,CAAC,IAAI,CAAC;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAGA,MAAM,sBAAsB,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;gBAErD,IAAI,SACF,OACE,2BAAC,4BAAa;8BACZ,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;;4BACnD,2BAAC,UAAI;gCAAC,MAAK;;;;;;4BACX,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAG;0CAC1B,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;;gBAOjC,IAAI,CAAC,YACH,OACE,2BAAC,4BAAa;8BACZ,2BAAC,UAAI;kCACH,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAS;;gCACnD,2BAAC,yBAAkB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;wCAAW,cAAc;oCAAG;;;;;;gCAC9E,2BAAC;oCAAM,OAAO;8CAAG;;;;;;gCACjB,2BAAC;oCAAK,MAAK;8CAAY;;;;;;gCACvB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAG;8CAC1B,2BAAC,YAAM;wCAAC,MAAK;wCAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWrF,IAAI,CAAC,qBACH,OACE,2BAAC,4BAAa;8BACZ,2BAAC,UAAI;kCACH,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,QACE,2BAAC,YAAM;gCAAC,MAAK;gCAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0CAAe;;;;;;;;;;;;;;;;;;;;;gBAW5E,MAAM,WAAW;oBACf;wBACE,KAAK;wBACL,OACE,2BAAC,WAAK;;gCACJ,2BAAC,mBAAY;;;;;gCAAG;;;;;;;wBAIpB,UACE,2BAAC,6BAAoB;4BACnB,YAAY;4BACZ,WAAW;;;;;;oBAGjB;oBACA;wBACE,KAAK;wBACL,OACE,2BAAC,WAAK;;gCACJ,2BAAC,mBAAY;;;;;gCAAG;;;;;;;wBAIpB,UACE,2BAAC,gCAAuB;4BACtB,YAAY;4BACZ,WAAW;;;;;;oBAGjB;oBACA;wBACE,KAAK;wBACL,OACE,2BAAC,WAAK;;gCACJ,2BAAC,sBAAe;;;;;gCAAG;;;;;;;wBAIvB,UACE,2BAAC,qBAAY;4BACX,YAAY;4BACZ,WAAW;;;;;;oBAGjB;iBACD;gBAED,OACE,2BAAC,4BAAa;oBACZ,OAAM;oBACN,UACE,2BAAC,WAAK;;4BACJ,2BAAC,YAAM;gCAAC,MAAK;gCAAQ,MAAM,2BAAC,mBAAY;;;;;;;;;;4BACxC,2BAAC;0CAAM,WAAW,IAAI;;;;;;4BACtB,2BAAC,SAAG;gCAAC,MAAM,2BAAC,oBAAa;;;;;gCAAK,OAAM;0CAAO;;;;;;;;;;;;oBAG/C,OAAO;wBACL,2BAAC,YAAM;4BAEL,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;sCAC7B;2BAFK;;;;;qBAKP;8BAED,2BAAC,UAAI;kCACH,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU;4BACV,OAAO;4BACP,MAAK;4BACL,aAAa;gCAAE,cAAc;4BAAG;;;;;;;;;;;;;;;;YAK1C;eA9JM;iBAAA;gBAgKN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC8Kf;;;2BAAA;;;;;;sEA9W2C;yCAmBpC;0CASA;yCAIqB;;;;;;;;;;YAG5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;gBACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;gBAEjC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,eAAe;oBACnB,IAAI;wBACF,WAAW;wBACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;wBAC1D,WAAW,cAAc,EAAE;oBAC7B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,WAAW,EAAE;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAGA,MAAM,sBAAsB,OAAO;oBACjC,IAAI;wBACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;wBAEnB,MAAM,iBAAW,CAAC,aAAa,CAAC;4BAAE,QAAQ;wBAAU;wBACpD,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC9C,sBAAsB;wBACtB,WAAW,WAAW;wBACtB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,qBAAqB,OAAO;oBAChC,IAAI;wBACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;wBACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;wBACtC;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,oBAAoB;oBACxB,IAAI;wBACF,MAAM,YAAY;wBAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;wBAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC7C,mBAAmB,EAAE;wBACrB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAI5D,MAAM,UAA2C;oBAC/C;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,SACV,2BAAC,WAAK;;oCACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAQ,MAAM,2BAAC,mBAAY;;;;;;;;;;oCACxC,2BAAC;;4CACC,2BAAC;0DACC,2BAAC,WAAK;;wDACJ,2BAAC;4DAAK,MAAM;sEAAE,OAAO,IAAI;;;;;;wDACxB,OAAO,SAAS,IACf,2BAAC,SAAG;4DAAC,MAAM,2BAAC,oBAAa;;;;;4DAAK,OAAM;sEAAO;;;;;;;;;;;;;;;;;4CAIjD,2BAAC;gDAAK,MAAK;gDAAY,OAAO;oDAAE,UAAU;gDAAG;0DAC1C,OAAO,KAAK;;;;;;;;;;;;;;;;;;oBAKvB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,WACP,2BAAC,SAAG;gCAAC,OAAO,WAAW,UAAU;0CAC9B,WAAW,OAAO;;;;;;oBAGzB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,OAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,OACE,2BAAC,gBAAU;gCACT,OAAM;gCACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;gCAChD,WAAW,IAAM,mBAAmB;gCACpC,QAAO;gCACP,YAAW;gCACX,QAAO;0CAEP,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAM;oCACN,MAAK;oCACL,MAAM,2BAAC,qBAAc;;;;;8CACtB;;;;;;;;;;;wBAKP;oBACF;iBACD;gBAGD,MAAM,eAAe;oBACnB;oBACA,UAAU;oBACV,kBAAkB,CAAC,SAAgC,CAAA;4BACjD,UAAU,OAAO,SAAS;wBAC5B,CAAA;gBACF;gBAEA,OACE,2BAAC;;wBAEC,2BAAC,SAAG;4BAAC,QAAQ;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;gCACzC,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;4CAC7B,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;4CACrD,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;4CACtD,QAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;gCAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,2BAAC,UAAI;kDACH,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;4CACtD,QAAQ,2BAAC,oBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;wBAO9B,2BAAC,UAAI;4BAAC,OAAO;gCAAE,cAAc;4BAAG;sCAC9B,2BAAC,SAAG;gCAAC,SAAQ;gCAAgB,OAAM;;oCACjC,2BAAC,SAAG;kDACF,2BAAC,WAAK;;gDACJ,2BAAC,WAAK;oDACJ,aAAY;oDACZ,QAAQ,2BAAC,qBAAc;;;;;oDACvB,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;wDAAE,OAAO;oDAAI;oDACpB,UAAU;;;;;;gDAEX,gBAAgB,MAAM,GAAG,KACxB,2BAAC,gBAAU;oDACT,OAAM;oDACN,aAAa,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;oDAC/D,WAAW;oDACX,QAAO;oDACP,YAAW;oDACX,QAAO;8DAEP,2BAAC,YAAM;wDACL,MAAM;wDACN,MAAM,2BAAC,qBAAc;;;;;;4DACtB;4DACQ,gBAAgB,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;oCAMxC,2BAAC,SAAG;kDACF,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM,2BAAC,sBAAe;;;;;4CACtB,SAAS,IAAM,sBAAsB;sDACtC;;;;;;;;;;;;;;;;;;;;;;wBAQP,2BAAC,UAAI;sCACH,2BAAC,WAAK;gCACJ,SAAS;gCACT,YAAY;gCACZ,QAAO;gCACP,SAAS;gCACT,cAAc;gCACd,YAAY;oCACV,iBAAiB;oCACjB,iBAAiB;oCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oCACtC,UAAU;gCACZ;;;;;;;;;;;wBAKJ,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,sBAAsB;gCACtB,WAAW,WAAW;4BACxB;4BACA,QAAQ;4BACR,OAAO;sCAEP,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;oCAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCACtC;wCACD,OAAM;kDAEN,2BAAC;4CACC,MAAM;4CACN,aAAY;;;;;;;;;;;oCAGhB,2BAAC,UAAI,CAAC,IAAI;kDACR,2BAAC,WAAK;;gDACJ,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,MAAM,2BAAC,mBAAY;;;;;8DAAK;;;;;;gDAGjE,2BAAC,YAAM;oDAAC,SAAS,IAAM,sBAAsB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASnE;eAjUM;;oBASiB,UAAI,CAAC;;;iBATtB;gBAmUN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IJ3XD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AAC33B"}