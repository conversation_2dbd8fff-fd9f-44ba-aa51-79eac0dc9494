import type { TeamDetailResponse, UserProfileResponse } from '@/types/api';

/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(
  initialState:
    | {
        currentUser?: UserProfileResponse;
        currentTeam?: TeamDetailResponse;
      }
    | undefined,
) {
  const { currentUser, currentTeam } = initialState ?? {};
  return {
    // 基于团队创建者身份判断管理权限
    canAdmin: currentUser && currentTeam && currentTeam.isCreator,
    isLoggedIn: !!currentUser,
    hasTeam: !!currentTeam,
  };
}
