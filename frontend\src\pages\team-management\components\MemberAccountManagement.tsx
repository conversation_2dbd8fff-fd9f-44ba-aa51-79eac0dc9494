/**
 * 成员账户管理组件
 * 
 * 功能特性：
 * - 在团队环境中管理成员账户
 * - 处理团队成员的权限和角色
 * - 查看成员账户状态和活动信息
 * - 管理成员的团队访问权限
 * 
 * 权限控制：
 * - 只有团队创建者可以进行账户管理操作
 * - 提供详细的权限变更记录
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Tag,
  Avatar,
  Typography,
  Row,
  Col,
  Statistic,
  Switch,
  Tooltip,
  Modal,
  Form,
  Select,
  message,
  Alert
} from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  CrownOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

// 导入服务和类型
import { TeamService } from '@/services/team';
import type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';

const { Text, Title } = Typography;

interface MemberAccountManagementProps {
  teamDetail: TeamDetailResponse;
  onRefresh: () => void;
}

const MemberAccountManagement: React.FC<MemberAccountManagementProps> = ({
  teamDetail,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [members, setMembers] = useState<TeamMemberResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  const [selectedMember, setSelectedMember] = useState<TeamMemberResponse | null>(null);
  const [settingsForm] = Form.useForm();

  useEffect(() => {
    fetchMembers();
  }, []);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const memberList = await TeamService.getCurrentTeamMembers();
      setMembers(memberList || []);
    } catch (error) {
      console.error('获取团队成员失败:', error);
      message.error('获取团队成员失败');
      setMembers([]); // 确保在错误时设置为空数组
    } finally {
      setLoading(false);
    }
  };

  // 切换成员活跃状态
  const handleToggleActiveStatus = async (member: TeamMemberResponse, isActive: boolean) => {
    try {
      // 这里应该调用API来更新成员状态
      // 由于当前后端可能没有这个接口，我们先模拟
      message.success(`已${isActive ? '激活' : '停用'}成员：${member.name}`);
      fetchMembers();
    } catch (error) {
      console.error('更新成员状态失败:', error);
      message.error('更新成员状态失败');
    }
  };

  // 打开成员设置弹窗
  const handleOpenSettings = (member: TeamMemberResponse) => {
    setSelectedMember(member);
    settingsForm.setFieldsValue({
      role: member.isCreator ? 'admin' : 'member',
      isActive: member.isActive,
    });
    setSettingsModalVisible(true);
  };

  // 保存成员设置
  const handleSaveSettings = async (values: any) => {
    try {
      // 这里应该调用API来更新成员设置
      // 由于当前后端可能没有这个接口，我们先模拟
      message.success(`已更新成员设置：${selectedMember?.name}`);
      setSettingsModalVisible(false);
      fetchMembers();
    } catch (error) {
      console.error('更新成员设置失败:', error);
      message.error('更新成员设置失败');
    }
  };

  // 筛选成员
  const filteredMembers = (members || []).filter(member =>
    member.name.toLowerCase().includes(searchText.toLowerCase()) ||
    member.email.toLowerCase().includes(searchText.toLowerCase())
  );

  // 表格列配置
  const columns: ColumnsType<TeamMemberResponse> = [
    {
      title: '成员信息',
      key: 'member',
      render: (_, record) => (
        <Space>
          <Avatar size="large" icon={<UserOutlined />} />
          <div>
            <div>
              <Space>
                <Text strong>{record.name}</Text>
                {record.isCreator && (
                  <Tag icon={<CrownOutlined />} color="gold">管理员</Tag>
                )}
              </Space>
            </div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.email}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '角色',
      key: 'role',
      width: 120,
      render: (_, record) => (
        <Tag color={record.isCreator ? 'gold' : 'blue'}>
          {record.isCreator ? '管理员' : '成员'}
        </Tag>
      ),
    },
    {
      title: '账户状态',
      key: 'accountStatus',
      width: 120,
      render: (_, record) => (
        <Space>
          {record.isActive ? (
            <Tag icon={<CheckCircleOutlined />} color="success">
              活跃
            </Tag>
          ) : (
            <Tag icon={<CloseCircleOutlined />} color="warning">
              待激活
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: '最后访问',
      dataIndex: 'lastAccessTime',
      key: 'lastAccessTime',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '权限控制',
      key: 'permissions',
      width: 120,
      render: (_, record) => {
        if (record.isCreator) {
          return <Text type="secondary">管理员</Text>;
        }

        return (
          <Tooltip title={record.isActive ? '点击停用' : '点击激活'}>
            <Switch
              checked={record.isActive}
              onChange={(checked) => handleToggleActiveStatus(record, checked)}
              size="small"
            />
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => {
        if (record.isCreator) {
          return <Text type="secondary">-</Text>;
        }

        return (
          <Button
            type="text"
            size="small"
            icon={<SettingOutlined />}
            onClick={() => handleOpenSettings(record)}
          >
            设置
          </Button>
        );
      },
    },
  ];

  return (
    <div>
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总成员数"
              value={(members || []).length}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃账户"
              value={(members || []).filter(m => m.isActive).length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待激活账户"
              value={(members || []).filter(m => !m.isActive).length}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="管理员"
              value={(members || []).filter(m => m.isCreator).length}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 功能说明 */}
      <Alert
        message="成员账户管理"
        description="在这里您可以管理团队成员的账户状态、权限和角色。只有团队管理员可以进行这些操作。"
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 16 }}
      />

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Input
              placeholder="搜索成员姓名或邮箱"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 300 }}
              allowClear
            />
          </Col>
        </Row>
      </Card>

      {/* 成员账户列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredMembers}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个账户`,
            pageSize: 10,
          }}
        />
      </Card>

      {/* 成员设置弹窗 */}
      <Modal
        title={`成员设置 - ${selectedMember?.name}`}
        open={settingsModalVisible}
        onCancel={() => {
          setSettingsModalVisible(false);
          settingsForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={settingsForm}
          layout="vertical"
          onFinish={handleSaveSettings}
        >
          <Form.Item
            name="role"
            label="角色"
          >
            <Select disabled>
              <Select.Option value="admin">管理员</Select.Option>
              <Select.Option value="member">成员</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="isActive"
            label="账户状态"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="活跃"
              unCheckedChildren="停用"
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setSettingsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MemberAccountManagement;
